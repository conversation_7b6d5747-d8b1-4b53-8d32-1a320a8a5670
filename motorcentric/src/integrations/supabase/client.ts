// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mpvtrahjwuuqiyjdlrxb.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1wdnRyYWhqd3V1cWl5amRscnhiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxMzQ1ODMsImV4cCI6MjA2ODcxMDU4M30.3LkEa1oCBnOy9xND70g8eBnFKjvPO5UapcNaYPtw3-4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});