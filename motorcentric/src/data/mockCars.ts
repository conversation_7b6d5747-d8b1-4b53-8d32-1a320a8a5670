import featuredCar from '@/assets/featured-car.jpg';

export interface MockCar {
  id: string;
  make: string;
  model: string;
  year: number;
  price: number;
  image_url: string;
  images: string[];
  description: string;
  specifications: {
    engine: string;
    horsepower: string;
    torque: string;
    transmission: string;
    drivetrain: string;
    acceleration: string;
    topSpeed: string;
    fuelEconomy: string;
    seating: string;
    bodyType: string;
    doors: string;
  };
  features: string[];
  condition: string;
  mileage: number;
  fuel_type: string;
  transmission: string;
  body_type: string;
  color: string;
  interior: string;
  vin: string;
  stockNumber: string;
  dealer: {
    name: string;
    phone: string;
    email: string;
    address: string;
  };
}

export const mockCars: MockCar[] = [
  {
    id: '1',
    make: 'BMW',
    model: 'M5 Competition',
    year: 2024,
    price: 18750000, // ~125k USD to KSh
    image_url: featuredCar,
    images: [featuredCar, featuredCar, featuredCar, featuredCar, featuredCar, featuredCar],
    description: 'Experience the pinnacle of luxury performance with the BMW M5 Competition. This exceptional sedan combines breathtaking power with sophisticated elegance, featuring a twin-turbo V8 engine that delivers 617 horsepower.',
    specifications: {
      engine: '4.4L Twin-Turbo V8',
      horsepower: '617 hp',
      torque: '553 lb-ft',
      transmission: '8-Speed Automatic',
      drivetrain: 'All-Wheel Drive',
      acceleration: '0-60 mph in 3.1s',
      topSpeed: '190 mph (limited)',
      fuelEconomy: '15/21 mpg',
      seating: '5 passengers',
      bodyType: 'Sedan',
      doors: '4 doors'
    },
    features: ['M Performance Exhaust', 'Carbon Fiber Roof', 'Adaptive M Suspension', 'M Carbon Ceramic Brakes', 'Harman Kardon Audio', 'Heated Seats'],
    condition: 'Excellent',
    mileage: 1200,
    fuel_type: 'Gasoline',
    transmission: 'Automatic',
    body_type: 'Sedan',
    color: 'Alpine White',
    interior: 'Black Merino Leather',
    vin: 'WBSJF0C55NCG12345',
    stockNumber: 'BMW2024001',
    dealer: {
      name: 'Motorcentric',
      phone: '+254723-398-457',
      email: '<EMAIL>',
      address: 'Fourways Kiambu road, Nairobi'
    }
  },
  {
    id: '2',
    make: 'Mercedes-Benz',
    model: 'AMG GT 63 S',
    year: 2024,
    price: 24750000, // ~165k USD to KSh
    image_url: featuredCar,
    images: [featuredCar, featuredCar, featuredCar, featuredCar, featuredCar],
    description: 'The Mercedes-AMG GT 63 S delivers uncompromising performance with its handcrafted AMG 4.0L V8 biturbo engine, producing 630 horsepower and featuring AMG Performance 4MATIC+ all-wheel drive.',
    specifications: {
      engine: '4.0L V8 Biturbo',
      horsepower: '630 hp',
      torque: '664 lb-ft',
      transmission: 'AMG SPEEDSHIFT DCT 8G',
      drivetrain: 'AMG Performance 4MATIC+',
      acceleration: '0-60 mph in 3.1s',
      topSpeed: '195 mph',
      fuelEconomy: '14/20 mpg',
      seating: '4 passengers',
      bodyType: 'Coupe',
      doors: '4 doors'
    },
    features: ['AMG Performance Exhaust', 'AMG Ride Control', 'AMG Track Pace', 'Burmester Audio', 'AMG Performance Seats', 'Carbon Fiber Trim'],
    condition: 'New',
    mileage: 850,
    fuel_type: 'Gasoline',
    transmission: 'Automatic',
    body_type: 'Coupe',
    color: 'Obsidian Black',
    interior: 'Red Nappa Leather',
    vin: 'WDDYJ7JA5PA123456',
    stockNumber: 'MB2024002',
    dealer: {
      name: 'Motorcentric',
      phone: '+254723-398-457',
      email: '<EMAIL>',
      address: 'Fourways Kiambu road, Nairobi'
    }
  },
  {
    id: '3',
    make: 'Porsche',
    model: '911 Turbo S',
    year: 2024,
    price: 34500000, // ~230k USD to KSh
    image_url: featuredCar,
    images: [featuredCar, featuredCar, featuredCar, featuredCar, featuredCar, featuredCar],
    description: 'The Porsche 911 Turbo S represents the pinnacle of sports car engineering, featuring a twin-turbocharged flat-six engine that produces 640 horsepower and legendary Porsche handling.',
    specifications: {
      engine: '3.8L Twin-Turbo Flat-6',
      horsepower: '640 hp',
      torque: '590 lb-ft',
      transmission: '8-Speed PDK',
      drivetrain: 'All-Wheel Drive',
      acceleration: '0-60 mph in 2.6s',
      topSpeed: '205 mph',
      fuelEconomy: '15/20 mpg',
      seating: '4 passengers',
      bodyType: 'Coupe',
      doors: '2 doors'
    },
    features: ['Sport Chrono Package', 'PASM', 'Ceramic Composite Brakes', 'Bose Audio', 'Sport Seats Plus', 'Carbon Fiber Interior'],
    condition: 'Excellent',
    mileage: 500,
    fuel_type: 'Gasoline',
    transmission: 'Automatic',
    body_type: 'Coupe',
    color: 'Guards Red',
    interior: 'Black Leather',
    vin: 'WP0AB2A99PS123456',
    stockNumber: 'POR2024003',
    dealer: {
      name: 'Motorcentric',
      phone: '+254723-398-457',
      email: '<EMAIL>',
      address: 'Fourways Kiambu road, Nairobi'
    }
  },
  {
    id: '4',
    make: 'Audi',
    model: 'RS7 Sportback',
    year: 2024,
    price: 21000000, // ~140k USD to KSh
    image_url: featuredCar,
    images: [featuredCar, featuredCar, featuredCar, featuredCar, featuredCar],
    description: 'The Audi RS7 Sportback combines stunning design with incredible performance, featuring a twin-turbo V8 engine and Quattro all-wheel drive for the ultimate grand touring experience.',
    specifications: {
      engine: '4.0L Twin-Turbo V8',
      horsepower: '591 hp',
      torque: '590 lb-ft',
      transmission: '8-Speed Tiptronic',
      drivetrain: 'Quattro AWD',
      acceleration: '0-60 mph in 3.5s',
      topSpeed: '174 mph (limited)',
      fuelEconomy: '15/22 mpg',
      seating: '5 passengers',
      bodyType: 'Sportback',
      doors: '5 doors'
    },
    features: ['Virtual Cockpit Plus', 'Matrix LED Headlights', 'Bang & Olufsen Audio', 'Sport Differential', 'Air Suspension', 'Carbon Fiber Trim'],
    condition: 'Excellent',
    mileage: 2100,
    fuel_type: 'Gasoline',
    transmission: 'Automatic',
    body_type: 'Sportback',
    color: 'Nardo Gray',
    interior: 'Black Valcona Leather',
    vin: 'WAUZZZ4G5PN123456',
    stockNumber: 'AUD2024004',
    dealer: {
      name: 'Motorcentric',
      phone: '+254723-398-457',
      email: '<EMAIL>',
      address: 'Fourways Kiambu road, Nairobi'
    }
  },
  {
    id: '5',
    make: 'Tesla',
    model: 'Model S Plaid',
    year: 2024,
    price: 16500000, // ~110k USD to KSh
    image_url: featuredCar,
    images: [featuredCar, featuredCar, featuredCar, featuredCar, featuredCar],
    description: 'The Tesla Model S Plaid redefines electric performance with its tri-motor setup delivering over 1,000 horsepower, advanced Autopilot, and cutting-edge technology.',
    specifications: {
      engine: 'Tri-Motor Electric',
      horsepower: '1,020 hp',
      torque: '1,050 lb-ft',
      transmission: 'Single-Speed',
      drivetrain: 'All-Wheel Drive',
      acceleration: '0-60 mph in 1.99s',
      topSpeed: '200 mph',
      fuelEconomy: '120 MPGe',
      seating: '5 passengers',
      bodyType: 'Sedan',
      doors: '4 doors'
    },
    features: ['Autopilot', 'Full Self-Driving Capability', 'Premium Audio', 'Glass Roof', 'Over-the-Air Updates', 'Supercharging'],
    condition: 'Excellent',
    mileage: 3000,
    fuel_type: 'Electric',
    transmission: 'Automatic',
    body_type: 'Sedan',
    color: 'Pearl White',
    interior: 'Black Premium',
    vin: '5YJ3E1EA8PF123456',
    stockNumber: 'TES2024005',
    dealer: {
      name: 'Motorcentric',
      phone: '+254723-398-457',
      email: '<EMAIL>',
      address: 'Fourways Kiambu road, Nairobi'
    }
  },
  {
    id: '6',
    make: 'Lamborghini',
    model: 'Huracán EVO',
    year: 2023,
    price: 42750000, // ~285k USD to KSh
    image_url: featuredCar,
    images: [featuredCar, featuredCar, featuredCar, featuredCar, featuredCar, featuredCar],
    description: 'The Lamborghini Huracán EVO delivers pure Italian supercar DNA with its naturally aspirated V10 engine, advanced aerodynamics, and track-focused performance.',
    specifications: {
      engine: '5.2L Naturally Aspirated V10',
      horsepower: '631 hp',
      torque: '443 lb-ft',
      transmission: '7-Speed Dual-Clutch',
      drivetrain: 'All-Wheel Drive',
      acceleration: '0-60 mph in 2.9s',
      topSpeed: '202 mph',
      fuelEconomy: '13/18 mpg',
      seating: '2 passengers',
      bodyType: 'Coupe',
      doors: '2 doors'
    },
    features: ['LDVI System', 'Carbon Fiber Aero Package', 'Alcantara Interior', 'Track Mode', 'Launch Control', 'Premium Audio'],
    condition: 'Excellent',
    mileage: 1500,
    fuel_type: 'Gasoline',
    transmission: 'Automatic',
    body_type: 'Coupe',
    color: 'Arancio Borealis',
    interior: 'Black Alcantara',
    vin: 'ZHWUC1ZF5PLA12345',
    stockNumber: 'LAM2023006',
    dealer: {
      name: 'Motorcentric',
      phone: '+254723-398-457',
      email: '<EMAIL>',
      address: 'Fourways Kiambu road, Nairobi'
    }
  }
];

// Car makes and their models for filtering
export const carMakesAndModels = {
  'BMW': ['M3', 'M4', 'M5', 'M8', 'X3 M', 'X4 M', 'X5 M', 'X6 M', '3 Series', '4 Series', '5 Series', '7 Series', 'X1', 'X2', 'X3', 'X4', 'X5', 'X6', 'X7', 'Z4', 'i3', 'i4', 'iX'],
  'Mercedes-Benz': ['A-Class', 'C-Class', 'E-Class', 'S-Class', 'CLA', 'CLS', 'GLA', 'GLB', 'GLC', 'GLE', 'GLS', 'G-Class', 'AMG GT', 'SL', 'SLC', 'EQS', 'EQE', 'EQC', 'EQA', 'EQB'],
  'Audi': ['A3', 'A4', 'A5', 'A6', 'A7', 'A8', 'Q3', 'Q4', 'Q5', 'Q7', 'Q8', 'TT', 'R8', 'RS3', 'RS4', 'RS5', 'RS6', 'RS7', 'e-tron', 'e-tron GT'],
  'Porsche': ['911', 'Boxster', 'Cayman', 'Panamera', 'Macan', 'Cayenne', 'Taycan', '718 Boxster', '718 Cayman', '911 Turbo', '911 GT3', 'Panamera Turbo'],
  'Tesla': ['Model S', 'Model 3', 'Model X', 'Model Y', 'Cybertruck', 'Roadster'],
  'Lamborghini': ['Huracán', 'Aventador', 'Urus', 'Gallardo', 'Murciélago', 'Huracán EVO', 'Aventador SVJ'],
  'Ferrari': ['488', '812', 'F8', 'SF90', 'Roma', 'Portofino', 'LaFerrari', 'California', '458', '599'],
  'McLaren': ['570S', '720S', '765LT', 'Artura', 'GT', 'P1', '650S', '675LT', '600LT'],
  'Bentley': ['Continental', 'Flying Spur', 'Bentayga', 'Mulsanne', 'Continental GT', 'Continental GTC'],
  'Rolls-Royce': ['Ghost', 'Phantom', 'Wraith', 'Dawn', 'Cullinan', 'Spectre'],
  'Aston Martin': ['DB11', 'Vantage', 'DBS', 'DBX', 'Rapide', 'Vanquish', 'DB9'],
  'Maserati': ['Ghibli', 'Quattroporte', 'Levante', 'GranTurismo', 'GranCabrio', 'MC20'],
  'Jaguar': ['XE', 'XF', 'XJ', 'F-Type', 'E-Pace', 'F-Pace', 'I-Pace'],
  'Land Rover': ['Range Rover', 'Range Rover Sport', 'Range Rover Evoque', 'Range Rover Velar', 'Discovery', 'Discovery Sport', 'Defender'],
  'Lexus': ['IS', 'ES', 'GS', 'LS', 'RC', 'LC', 'NX', 'RX', 'GX', 'LX', 'UX'],
  'Toyota': ['Camry', 'Corolla', 'Prius', 'RAV4', 'Highlander', '4Runner', 'Tacoma', 'Tundra', 'Sienna', 'Avalon', 'Supra', 'GR86'],
  'Honda': ['Civic', 'Accord', 'CR-V', 'Pilot', 'Passport', 'Ridgeline', 'HR-V', 'Insight', 'Odyssey', 'Fit'],
  'Nissan': ['Altima', 'Sentra', 'Maxima', 'Rogue', 'Murano', 'Pathfinder', 'Armada', 'Frontier', 'Titan', '370Z', 'GT-R'],
  'Ford': ['F-150', 'Mustang', 'Explorer', 'Escape', 'Edge', 'Expedition', 'Bronco', 'Ranger', 'Fusion', 'Focus'],
  'Chevrolet': ['Silverado', 'Camaro', 'Corvette', 'Equinox', 'Traverse', 'Tahoe', 'Suburban', 'Colorado', 'Malibu', 'Cruze']
};
