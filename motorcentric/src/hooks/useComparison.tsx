import { createContext, useContext, useState, ReactNode } from 'react';
import { Car } from './useCars';
import { toast } from 'sonner';

interface ComparisonContextType {
  comparisonCars: Car[];
  addToComparison: (car: Car) => void;
  removeFromComparison: (carId: string) => void;
  clearComparison: () => void;
  isInComparison: (carId: string) => boolean;
  maxComparisons: number;
}

const ComparisonContext = createContext<ComparisonContextType | undefined>(undefined);

interface ComparisonProviderProps {
  children: ReactNode;
}

export const ComparisonProvider = ({ children }: ComparisonProviderProps) => {
  const [comparisonCars, setComparisonCars] = useState<Car[]>([]);
  const maxComparisons = 3;

  const addToComparison = (car: Car) => {
    setComparisonCars(prev => {
      // Check if car is already in comparison
      if (prev.some(c => c.id === car.id)) {
        toast.info('This car is already in your comparison');
        return prev;
      }

      // Check if we've reached the maximum
      if (prev.length >= maxComparisons) {
        toast.error(`You can only compare up to ${maxComparisons} cars at once`);
        return prev;
      }

      toast.success(`${car.make} ${car.model} added to comparison`);
      return [...prev, car];
    });
  };

  const removeFromComparison = (carId: string) => {
    setComparisonCars(prev => {
      const car = prev.find(c => c.id === carId);
      if (car) {
        toast.success(`${car.make} ${car.model} removed from comparison`);
      }
      return prev.filter(c => c.id !== carId);
    });
  };

  const clearComparison = () => {
    setComparisonCars([]);
    toast.success('Comparison cleared');
  };

  const isInComparison = (carId: string) => {
    return comparisonCars.some(car => car.id === carId);
  };

  return (
    <ComparisonContext.Provider
      value={{
        comparisonCars,
        addToComparison,
        removeFromComparison,
        clearComparison,
        isInComparison,
        maxComparisons
      }}
    >
      {children}
    </ComparisonContext.Provider>
  );
};

export const useComparison = () => {
  const context = useContext(ComparisonContext);
  if (context === undefined) {
    throw new Error('useComparison must be used within a ComparisonProvider');
  }
  return context;
};
