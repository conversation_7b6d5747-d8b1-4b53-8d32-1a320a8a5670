import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface AnalyticsEvent {
  event_type: string;
  car_id?: string;
  page_url?: string;
  referrer?: string;
  metadata?: any;
}

export const useAnalytics = () => {
  const trackEvent = async (event: AnalyticsEvent) => {
    try {
      await supabase
        .from('analytics')
        .insert([
          {
            ...event,
            session_id: getSessionId(),
            user_agent: navigator.userAgent,
            created_at: new Date().toISOString()
          }
        ]);
    } catch (error) {
      console.error('Analytics tracking error:', error);
    }
  };

  const trackPageView = (page_url: string) => {
    trackEvent({
      event_type: 'page_view',
      page_url,
      referrer: document.referrer
    });
  };

  const trackCarView = (car_id: string) => {
    trackEvent({
      event_type: 'car_view',
      car_id,
      page_url: window.location.href
    });
  };

  const trackInquiry = (car_id?: string, inquiry_type?: string) => {
    trackEvent({
      event_type: 'inquiry',
      car_id,
      metadata: { inquiry_type }
    });
  };

  const trackSearch = (filters: any) => {
    trackEvent({
      event_type: 'search',
      metadata: { filters }
    });
  };

  return {
    trackEvent,
    trackPageView,
    trackCarView,
    trackInquiry,
    trackSearch
  };
};

// Simple session ID generator
const getSessionId = () => {
  let sessionId = sessionStorage.getItem('analytics_session_id');
  if (!sessionId) {
    sessionId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    sessionStorage.setItem('analytics_session_id', sessionId);
  }
  return sessionId;
};

// Hook to track page views automatically
export const usePageTracking = () => {
  const { trackPageView } = useAnalytics();

  useEffect(() => {
    trackPageView(window.location.pathname);
  }, [trackPageView]);
};
