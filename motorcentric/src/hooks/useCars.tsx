import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Tables } from '@/integrations/supabase/types';

export type Car = Tables<'cars'>;

interface UseCarFilters {
  searchTerm?: string;
  make?: string;
  model?: string;
  minPrice?: number;
  maxPrice?: number;
  minYear?: number;
  maxYear?: number;
  minMileage?: number;
  maxMileage?: number;
  fuelType?: string[];
  transmission?: string[];
  bodyType?: string[];
  color?: string[];
  condition?: string[];
  features?: string[];
  category?: string;
}

interface UseCarsReturn {
  cars: Car[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  refetch: () => void;
}

export const useCars = (
  filters: UseCarFilters = {},
  page: number = 1,
  pageSize: number = 12
): UseCarsReturn => {
  const [cars, setCars] = useState<Car[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  const fetchCars = async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase
        .from('cars')
        .select('*', { count: 'exact' });

      // Apply filters
      if (filters.searchTerm) {
        query = query.or(`make.ilike.%${filters.searchTerm}%,model.ilike.%${filters.searchTerm}%,description.ilike.%${filters.searchTerm}%`);
      }

      if (filters.make) {
        query = query.eq('make', filters.make);
      }

      if (filters.model) {
        query = query.eq('model', filters.model);
      }

      if (filters.minPrice !== undefined) {
        query = query.gte('price', filters.minPrice);
      }

      if (filters.maxPrice !== undefined) {
        query = query.lte('price', filters.maxPrice);
      }

      if (filters.minYear !== undefined) {
        query = query.gte('year', filters.minYear);
      }

      if (filters.maxYear !== undefined) {
        query = query.lte('year', filters.maxYear);
      }

      if (filters.minMileage !== undefined) {
        query = query.gte('mileage', filters.minMileage);
      }

      if (filters.maxMileage !== undefined) {
        query = query.lte('mileage', filters.maxMileage);
      }

      if (filters.fuelType && filters.fuelType.length > 0) {
        query = query.in('fuel_type', filters.fuelType);
      }

      if (filters.transmission && filters.transmission.length > 0) {
        query = query.in('transmission', filters.transmission);
      }

      if (filters.bodyType && filters.bodyType.length > 0) {
        query = query.in('body_type', filters.bodyType);
      }

      if (filters.color && filters.color.length > 0) {
        query = query.in('color', filters.color);
      }

      if (filters.category && filters.category !== 'all') {
        query = query.eq('category', filters.category);
      }

      if (filters.condition && filters.condition.length > 0) {
        query = query.in('condition', filters.condition);
      }

      if (filters.features && filters.features.length > 0) {
        // For features array, we need to check if any of the selected features exist in the car's features array
        const featureConditions = filters.features.map(feature => `features.cs.{${feature}}`);
        query = query.or(featureConditions.join(','));
      }

      // Apply pagination
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);

      // Order by created_at desc by default
      query = query.order('created_at', { ascending: false });

      const { data, error: fetchError, count } = await query;

      if (fetchError) {
        throw fetchError;
      }

      setCars(data || []);
      setTotalCount(count || 0);
    } catch (err) {
      console.error('Error fetching cars:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while fetching cars');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCars();
  }, [
    filters.searchTerm,
    filters.make,
    filters.model,
    filters.minPrice,
    filters.maxPrice,
    filters.minYear,
    filters.maxYear,
    filters.minMileage,
    filters.maxMileage,
    filters.fuelType?.join(','),
    filters.transmission?.join(','),
    filters.bodyType?.join(','),
    filters.color?.join(','),
    filters.condition?.join(','),
    filters.features?.join(','),
    page,
    pageSize
  ]);

  return {
    cars,
    loading,
    error,
    totalCount,
    refetch: fetchCars
  };
};

// Hook to get URL-based filters
export const useCarFiltersFromUrl = (): UseCarFilters => {
  const [searchParams] = useSearchParams();

  return {
    searchTerm: searchParams.get('searchTerm') || undefined,
    make: searchParams.get('make') || undefined,
    model: searchParams.get('model') || undefined,
    minPrice: searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : undefined,
    maxPrice: searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : undefined,
    minYear: searchParams.get('minYear') ? parseInt(searchParams.get('minYear')!) : undefined,
    maxYear: searchParams.get('maxYear') ? parseInt(searchParams.get('maxYear')!) : undefined,
    minMileage: searchParams.get('minMileage') ? parseInt(searchParams.get('minMileage')!) : undefined,
    maxMileage: searchParams.get('maxMileage') ? parseInt(searchParams.get('maxMileage')!) : undefined,
    fuelType: searchParams.get('fuelType')?.split(',').filter(Boolean) || undefined,
    transmission: searchParams.get('transmission')?.split(',').filter(Boolean) || undefined,
    bodyType: searchParams.get('bodyType')?.split(',').filter(Boolean) || undefined,
    color: searchParams.get('color')?.split(',').filter(Boolean) || undefined,
    condition: searchParams.get('condition')?.split(',').filter(Boolean) || undefined,
    features: searchParams.get('features')?.split(',').filter(Boolean) || undefined,
    category: searchParams.get('category') || 'all',
  };
};

// Hook to get a single car by ID
export const useCar = (id: string) => {
  const [car, setCar] = useState<Car | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCar = async () => {
      try {
        setLoading(true);
        setError(null);

        const { data, error: fetchError } = await supabase
          .from('cars')
          .select('*')
          .eq('id', id)
          .single();

        if (fetchError) {
          throw fetchError;
        }

        setCar(data);
      } catch (err) {
        console.error('Error fetching car:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching the car');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchCar();
    }
  }, [id]);

  return { car, loading, error };
};
