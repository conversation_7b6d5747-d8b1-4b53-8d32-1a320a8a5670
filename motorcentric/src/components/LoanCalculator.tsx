import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calculator } from 'lucide-react';

const LoanCalculator = () => {
  const [loanAmount, setLoanAmount] = useState('100000');
  const [interestRate, setInterestRate] = useState('12');
  const [loanTerm, setLoanTerm] = useState('12');

  // Calculate monthly payment
  const calculatePayment = () => {
    const principal = parseFloat(loanAmount) || 0;
    const monthlyRate = (parseFloat(interestRate) || 0) / 100 / 12;
    const numberOfPayments = parseInt(loanTerm) || 1;
    
    if (monthlyRate === 0) {
      return principal / numberOfPayments;
    }
    
    const monthlyPayment = principal * 
      (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) / 
      (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
    
    return monthlyPayment;
  };

  const monthlyPayment = calculatePayment();
  const totalPayment = monthlyPayment * (parseInt(loanTerm) || 1);

  return (
    <section className="py-8 md:py-16 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8 md:mb-12">
          <h2 className="text-2xl md:text-4xl font-bold text-charcoal mb-4">
            Loan Calculator
          </h2>
          <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
            Calculate your monthly payments and plan your dream car purchase
          </p>
        </div>

        <div className="max-w-lg mx-auto">
          <Card className="shadow-elegant bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center text-charcoal text-xl md:text-2xl">
                <Calculator className="mr-2 h-5 w-5 md:h-6 md:w-6 text-gold" />
                Loan Calculator
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 md:space-y-6">
              {/* Loan Amount */}
              <div>
                <Label className="text-sm md:text-base font-semibold text-charcoal mb-2 block">
                  Loan Amount (KES)
                </Label>
                <Input
                  type="number"
                  value={loanAmount}
                  onChange={(e) => setLoanAmount(e.target.value)}
                  className="w-full text-base md:text-lg h-12 md:h-14"
                  placeholder="100000"
                />
              </div>

              {/* Interest Rate */}
              <div>
                <Label className="text-sm md:text-base font-semibold text-charcoal mb-2 block">
                  Annual Interest Rate (%)
                </Label>
                <Input
                  type="number"
                  step="0.1"
                  value={interestRate}
                  onChange={(e) => setInterestRate(e.target.value)}
                  className="w-full text-base md:text-lg h-12 md:h-14"
                  placeholder="12"
                />
              </div>

              {/* Loan Term */}
              <div>
                <Label className="text-sm md:text-base font-semibold text-charcoal mb-2 block">
                  Loan Term (Months)
                </Label>
                <Input
                  type="number"
                  value={loanTerm}
                  onChange={(e) => setLoanTerm(e.target.value)}
                  className="w-full text-base md:text-lg h-12 md:h-14"
                  placeholder="12"
                />
              </div>

              {/* Results */}
              <div className="mt-6 md:mt-8 space-y-4 pt-4 border-t border-amber-200">
                <div className="flex justify-between items-center">
                  <span className="text-sm md:text-base font-medium text-charcoal">
                    Monthly Repayment (KES):
                  </span>
                  <span className="text-lg md:text-xl font-bold text-blue-600">
                    {monthlyPayment.toLocaleString('en-KE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm md:text-base font-medium text-charcoal">
                    Total Repayment (KES):
                  </span>
                  <span className="text-lg md:text-xl font-bold text-blue-600">
                    {totalPayment.toLocaleString('en-KE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default LoanCalculator;