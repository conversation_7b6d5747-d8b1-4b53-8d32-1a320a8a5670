import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { MessageCircle, X } from 'lucide-react';

const FloatingWhatsApp = () => {
  const [isOpen, setIsOpen] = useState(false);
  const phoneNumber = '254723398457'; // Motorcentric WhatsApp number
  
  const quickMessages = [
    'Hi, I would like to inquire about your vehicles.',
    'Can you provide more information about your car inventory?',
    'I would like to schedule a test drive.',
    'What financing options do you have available?',
    'Do you accept trade-ins?'
  ];

  const handleWhatsAppClick = (message?: string) => {
    const defaultMessage = 'Hi, I would like to inquire about your vehicles at Motorcentric.';
    const whatsappMessage = message || defaultMessage;
    const encodedMessage = encodeURIComponent(whatsappMessage);
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
    window.open(whatsappUrl, '_blank');
    setIsOpen(false);
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Quick Messages Card */}
      {isOpen && (
        <Card className="mb-4 w-80 shadow-lg animate-in slide-in-from-bottom-2">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-semibold text-charcoal">Quick Messages</h4>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="space-y-2">
              {quickMessages.map((message, index) => (
                <button
                  key={index}
                  onClick={() => handleWhatsAppClick(message)}
                  className="w-full text-left p-2 text-sm rounded-md hover:bg-muted transition-colors"
                >
                  {message}
                </button>
              ))}
            </div>
            <div className="mt-3 pt-3 border-t">
              <div className="text-xs text-muted-foreground text-center">
                <p className="font-medium">Motorcentric</p>
                <p>+254723-398-457</p>
                <p>Fourways Kiambu road, Nairobi</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* WhatsApp Button */}
      <Button
        onClick={() => setIsOpen(!isOpen)}
        className="h-14 w-14 rounded-full bg-green-500 hover:bg-green-600 shadow-lg animate-bounce"
        size="icon"
      >
        <MessageCircle className="h-6 w-6 text-white" />
      </Button>
    </div>
  );
};

export default FloatingWhatsApp;
