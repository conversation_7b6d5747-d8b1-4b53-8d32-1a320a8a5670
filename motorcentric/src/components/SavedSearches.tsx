import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { 
  Bookmark, 
  BookmarkPlus, 
  Search, 
  Trash2, 
  MoreVertical,
  Clock,
  Filter
} from 'lucide-react';
import { toast } from 'sonner';

interface SavedSearch {
  id: string;
  name: string;
  filters: Record<string, any>;
  createdAt: Date;
  lastUsed: Date;
}

const SavedSearches = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [searchName, setSearchName] = useState('');

  // Load saved searches from localStorage on component mount
  useEffect(() => {
    const saved = localStorage.getItem('motorcentric-saved-searches');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setSavedSearches(parsed.map((search: any) => ({
          ...search,
          createdAt: new Date(search.createdAt),
          lastUsed: new Date(search.lastUsed)
        })));
      } catch (error) {
        console.error('Error loading saved searches:', error);
      }
    }
  }, []);

  // Save searches to localStorage whenever the state changes
  useEffect(() => {
    localStorage.setItem('motorcentric-saved-searches', JSON.stringify(savedSearches));
  }, [savedSearches]);

  const getCurrentFilters = () => {
    const filters: Record<string, any> = {};
    searchParams.forEach((value, key) => {
      filters[key] = value;
    });
    return filters;
  };

  const saveCurrentSearch = () => {
    if (!searchName.trim()) {
      toast.error('Please enter a name for your search');
      return;
    }

    const filters = getCurrentFilters();
    if (Object.keys(filters).length === 0) {
      toast.error('No search criteria to save');
      return;
    }

    const newSearch: SavedSearch = {
      id: Date.now().toString(),
      name: searchName.trim(),
      filters,
      createdAt: new Date(),
      lastUsed: new Date()
    };

    setSavedSearches(prev => [newSearch, ...prev]);
    setSearchName('');
    setIsDialogOpen(false);
    toast.success('Search saved successfully!');
  };

  const loadSearch = (search: SavedSearch) => {
    // Update last used timestamp
    setSavedSearches(prev => 
      prev.map(s => 
        s.id === search.id 
          ? { ...s, lastUsed: new Date() }
          : s
      )
    );

    // Navigate with the saved filters
    const params = new URLSearchParams(search.filters);
    navigate(`/search?${params.toString()}`);
    toast.success(`Loaded search: ${search.name}`);
  };

  const deleteSearch = (searchId: string) => {
    setSavedSearches(prev => prev.filter(s => s.id !== searchId));
    toast.success('Search deleted');
  };

  const getFilterSummary = (filters: Record<string, any>) => {
    const summary = [];
    if (filters.make) summary.push(`Make: ${filters.make}`);
    if (filters.model) summary.push(`Model: ${filters.model}`);
    if (filters.minPrice || filters.maxPrice) {
      const min = filters.minPrice ? `KSh ${parseInt(filters.minPrice).toLocaleString()}` : 'KSh 0';
      const max = filters.maxPrice ? `KSh ${parseInt(filters.maxPrice).toLocaleString()}` : 'KSh 50M+';
      summary.push(`Price: ${min} - ${max}`);
    }
    if (filters.location) summary.push(`Location: ${filters.location}`);
    if (filters.fuelType) summary.push(`Fuel: ${filters.fuelType}`);
    
    return summary.slice(0, 3); // Show only first 3 filters
  };

  const hasCurrentFilters = Object.keys(getCurrentFilters()).length > 0;

  if (savedSearches.length === 0 && !hasCurrentFilters) {
    return null;
  }

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center">
            <Bookmark className="h-5 w-5 mr-2 text-gold" />
            Saved Searches
          </CardTitle>
          
          {hasCurrentFilters && (
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <BookmarkPlus className="h-4 w-4 mr-1" />
                  Save Current Search
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Save Search</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="search-name">Search Name</Label>
                    <Input
                      id="search-name"
                      placeholder="e.g., BMW under $50k"
                      value={searchName}
                      onChange={(e) => setSearchName(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && saveCurrentSearch()}
                    />
                  </div>
                  
                  <div>
                    <Label>Current Filters</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {getFilterSummary(getCurrentFilters()).map((filter, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {filter}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={saveCurrentSearch} className="flex-1">
                      Save Search
                    </Button>
                    <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </CardHeader>
      
      {savedSearches.length > 0 && (
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {savedSearches.slice(0, 6).map((search) => (
              <Card key={search.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold text-sm truncate flex-1">
                      {search.name}
                    </h4>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <MoreVertical className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => loadSearch(search)}>
                          <Search className="h-4 w-4 mr-2" />
                          Load Search
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => deleteSearch(search.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex flex-wrap gap-1">
                      {getFilterSummary(search.filters).map((filter, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {filter}
                        </Badge>
                      ))}
                      {Object.keys(search.filters).length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{Object.keys(search.filters).length - 3} more
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock className="h-3 w-3 mr-1" />
                      Last used: {search.lastUsed.toLocaleDateString()}
                    </div>
                  </div>
                  
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="w-full mt-3"
                    onClick={() => loadSearch(search)}
                  >
                    <Search className="h-4 w-4 mr-1" />
                    Load Search
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {savedSearches.length > 6 && (
            <div className="text-center mt-4">
              <Button variant="outline" size="sm">
                View All Saved Searches ({savedSearches.length})
              </Button>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export default SavedSearches;
