import { Card, CardContent } from '@/components/ui/card';
import { Car, Users, Award, Clock, Star, MapPin } from 'lucide-react';

const stats = [
  {
    icon: Car,
    number: '50+',
    label: 'Premium Vehicles',
    description: 'Carefully curated collection'
  },
  {
    icon: Users,
    number: '500+',
    label: 'Happy Customers',
    description: 'Satisfied clients in Kenya'
  },
  {
    icon: Award,
    number: '5+',
    label: 'Years Experience',
    description: 'Industry expertise'
  },
  {
    icon: Clock,
    number: '24/7',
    label: 'Customer Support',
    description: 'Always here to help'
  },
  {
    icon: Star,
    number: '4.9',
    label: 'Customer Rating',
    description: 'Exceptional service quality'
  },
  {
    icon: MapPin,
    number: '1',
    label: 'Prime Location',
    description: 'Nairobi showroom'
  }
];

const StatsSection = () => {
  return (
    <section className="py-8 md:py-16 bg-gradient-elegant">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8 md:mb-12">
          <h2 className="text-2xl md:text-4xl font-bold text-white mb-4">
            Our Performance
          </h2>
          <p className="text-lg md:text-xl text-gray-200 max-w-2xl mx-auto">
            Leading the luxury automotive industry with excellence and innovation
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">
          {stats.map((stat, index) => (
            <Card 
              key={index}
              className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300 group"
            >
              <CardContent className="p-4 md:p-8 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 md:w-16 md:h-16 bg-gold rounded-full mb-4 md:mb-6 group-hover:scale-110 transition-transform duration-300">
                  <stat.icon className="h-6 w-6 md:h-8 md:w-8 text-white" />
                </div>
                
                <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-gold mb-1 md:mb-2">
                  {stat.number}
                </div>
                
                <h3 className="text-lg md:text-xl font-semibold text-white mb-1 md:mb-2">
                  {stat.label}
                </h3>
                
                <p className="text-gray-300 text-sm md:text-base">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default StatsSection;