import { But<PERSON> } from '@/components/ui/button';
import { Search, Star, Award } from 'lucide-react';
import { Link } from 'react-router-dom';
import heroImage from '@/assets/hero-cars.jpg';

const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex items-center">
      {/* Background Image */}
      <div className="absolute inset-0">
        <img
          src={heroImage}
          alt="Luxury Cars"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/50"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-charcoal/80 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="relative container mx-auto px-4 text-white">
        <div className="max-w-3xl">
          <div className="flex items-center mb-4 md:mb-6 animate-fade-in">
            <Star className="h-5 w-5 md:h-6 md:w-6 text-gold mr-2" />
            <span className="text-gold font-semibold text-base md:text-lg">Premium Car Dealership - Nairobi</span>
          </div>
          
          <h1 className="text-3xl md:text-5xl lg:text-7xl font-bold mb-4 md:mb-6 leading-tight animate-fade-in">
            Find Your
            <span className="block text-transparent bg-clip-text bg-gradient-gold">
              Dream Car
            </span>
          </h1>
          
          <p className="text-lg md:text-xl lg:text-2xl mb-6 md:mb-8 text-gray-200 max-w-2xl animate-fade-in">
            Discover luxury vehicles from the world's finest manufacturers in Kenya.
            Your perfect car awaits at Motorcentric.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 mb-8 md:mb-12 animate-fade-in">
            <Link to="/search">
              <Button variant="gold" size="lg" className="text-base md:text-lg px-6 md:px-8 py-4 md:py-6 w-full">
                <Search className="mr-2" />
                Browse Cars
              </Button>
            </Link>
            <Link to="/contact">
              <Button variant="premium" size="lg" className="text-base md:text-lg px-6 md:px-8 py-4 md:py-6 w-full">
                <Award className="mr-2" />
                Contact Us
              </Button>
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 md:gap-8 animate-slide-in">
            <div className="text-center">
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-gold mb-1 md:mb-2">50+</div>
              <div className="text-gray-300 text-sm md:text-base">Premium Cars</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-gold mb-1 md:mb-2">5+</div>
              <div className="text-gray-300 text-sm md:text-base">Years Experience</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-gold mb-1 md:mb-2">500+</div>
              <div className="text-gray-300 text-sm md:text-base">Happy Clients</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="w-6 h-10 border-2 border-gold rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gold rounded-full mt-2 animate-bounce"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;