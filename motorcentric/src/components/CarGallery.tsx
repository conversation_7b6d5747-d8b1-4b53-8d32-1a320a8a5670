import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  Download,
  Maximize2,
  Grid3X3,
  Play,
  Pause,
  SkipBack,
  SkipForward
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface CarGalleryProps {
  images: string[];
  carName: string;
  year?: number;
  className?: string;
}

const CarGallery = ({ images, carName, year, className }: CarGalleryProps) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [isSlideshow, setIsSlideshow] = useState(false);
  const [showThumbnails, setShowThumbnails] = useState(true);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  
  const imageRef = useRef<HTMLImageElement>(null);
  const slideshowRef = useRef<NodeJS.Timeout>();

  // Auto-slideshow functionality
  useEffect(() => {
    if (isSlideshow && isModalOpen) {
      slideshowRef.current = setInterval(() => {
        setCurrentImageIndex(prev => (prev + 1) % images.length);
      }, 3000);
    } else {
      if (slideshowRef.current) {
        clearInterval(slideshowRef.current);
      }
    }

    return () => {
      if (slideshowRef.current) {
        clearInterval(slideshowRef.current);
      }
    };
  }, [isSlideshow, isModalOpen, images.length]);

  // Reset zoom and position when image changes
  useEffect(() => {
    setZoomLevel(1);
    setRotation(0);
    setImagePosition({ x: 0, y: 0 });
  }, [currentImageIndex]);

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.5, 3));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.5, 0.5));
  };

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = images[currentImageIndex];
    link.download = `${carName}-image-${currentImageIndex + 1}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoomLevel > 1) {
      setIsDragging(true);
      setDragStart({ x: e.clientX - imagePosition.x, y: e.clientY - imagePosition.y });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && zoomLevel > 1) {
      setImagePosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (!isModalOpen) return;
    
    switch (e.key) {
      case 'ArrowLeft':
        prevImage();
        break;
      case 'ArrowRight':
        nextImage();
        break;
      case 'Escape':
        setIsModalOpen(false);
        break;
      case '+':
      case '=':
        handleZoomIn();
        break;
      case '-':
        handleZoomOut();
        break;
      case 'r':
      case 'R':
        handleRotate();
        break;
      case ' ':
        e.preventDefault();
        setIsSlideshow(prev => !prev);
        break;
    }
  };

  useEffect(() => {
    if (isModalOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isModalOpen, currentImageIndex]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Main Image Display */}
      <div className="relative group">
        <div className="relative overflow-hidden rounded-lg shadow-elegant bg-muted">
          <img
            src={images[currentImageIndex]}
            alt={`${carName} view ${currentImageIndex + 1}`}
            className="w-full h-96 lg:h-[500px] object-cover transition-transform duration-300 group-hover:scale-105"
          />
          
          {/* Navigation Arrows */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={prevImage}
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={nextImage}
          >
            <ChevronRight className="h-6 w-6" />
          </Button>

          {/* Action Buttons */}
          <div className="absolute top-4 right-4 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="ghost"
              size="icon"
              className="bg-white/90 hover:bg-white"
              onClick={() => setIsModalOpen(true)}
            >
              <Maximize2 className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="bg-white/90 hover:bg-white"
              onClick={() => setShowThumbnails(!showThumbnails)}
            >
              <Grid3X3 className="h-5 w-5" />
            </Button>
          </div>

          {/* Image Counter and Year Badge */}
          <div className="absolute bottom-4 left-4 right-4 flex justify-between items-end">
            {year && (
              <Badge className="bg-gold text-white">
                {year}
              </Badge>
            )}
            <div className="bg-black/70 text-white px-3 py-1 rounded-full text-sm">
              {currentImageIndex + 1} / {images.length}
            </div>
          </div>
        </div>
      </div>

      {/* Thumbnail Grid */}
      {showThumbnails && (
        <div className="grid grid-cols-6 gap-2">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => setCurrentImageIndex(index)}
              className={cn(
                "relative h-16 rounded-md overflow-hidden border-2 transition-all",
                index === currentImageIndex 
                  ? "border-gold shadow-md scale-105" 
                  : "border-transparent hover:border-gold/50 hover:scale-102"
              )}
            >
              <img
                src={image}
                alt={`${carName} thumbnail ${index + 1}`}
                className="w-full h-full object-cover"
              />
              {index === currentImageIndex && (
                <div className="absolute inset-0 bg-gold/20" />
              )}
            </button>
          ))}
        </div>
      )}

      {/* Advanced Gallery Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-[95vw] max-h-[95vh] p-0 bg-black">
          <div className="relative w-full h-[95vh] flex flex-col">
            {/* Top Controls */}
            <div className="absolute top-4 left-4 right-4 z-10 flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="bg-white/90 text-black">
                  {currentImageIndex + 1} / {images.length}
                </Badge>
                <Badge variant="secondary" className="bg-white/90 text-black">
                  {Math.round(zoomLevel * 100)}%
                </Badge>
              </div>
              
              <div className="flex items-center gap-2">
                {/* Slideshow Controls */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="bg-white/90 hover:bg-white text-black"
                  onClick={() => setIsSlideshow(!isSlideshow)}
                >
                  {isSlideshow ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                
                {/* Navigation */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="bg-white/90 hover:bg-white text-black"
                  onClick={prevImage}
                >
                  <SkipBack className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="bg-white/90 hover:bg-white text-black"
                  onClick={nextImage}
                >
                  <SkipForward className="h-4 w-4" />
                </Button>
                
                {/* Zoom Controls */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="bg-white/90 hover:bg-white text-black"
                  onClick={handleZoomOut}
                  disabled={zoomLevel <= 0.5}
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="bg-white/90 hover:bg-white text-black"
                  onClick={handleZoomIn}
                  disabled={zoomLevel >= 3}
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                
                {/* Rotate */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="bg-white/90 hover:bg-white text-black"
                  onClick={handleRotate}
                >
                  <RotateCw className="h-4 w-4" />
                </Button>
                
                {/* Download */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="bg-white/90 hover:bg-white text-black"
                  onClick={handleDownload}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Main Image */}
            <div 
              className="flex-1 flex items-center justify-center overflow-hidden cursor-move"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            >
              <img
                ref={imageRef}
                src={images[currentImageIndex]}
                alt={`${carName} view ${currentImageIndex + 1}`}
                className="max-w-none transition-transform duration-200"
                style={{
                  transform: `scale(${zoomLevel}) rotate(${rotation}deg) translate(${imagePosition.x}px, ${imagePosition.y}px)`,
                  cursor: zoomLevel > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default'
                }}
                draggable={false}
              />
            </div>

            {/* Bottom Thumbnail Strip */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10">
              <div className="flex gap-2 bg-black/50 p-2 rounded-lg">
                {images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={cn(
                      "w-12 h-8 rounded overflow-hidden border transition-all",
                      index === currentImageIndex 
                        ? "border-gold scale-110" 
                        : "border-white/30 hover:border-white/60"
                    )}
                  >
                    <img
                      src={image}
                      alt={`Thumbnail ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CarGallery;
