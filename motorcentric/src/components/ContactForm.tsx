import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { 
  Phone, 
  Mail, 
  MessageSquare, 
  Calendar, 
  Clock,
  MapPin,
  User,
  Car,
  Send,
  CheckCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

interface ContactFormProps {
  carId?: string;
  carName?: string;
  dealerInfo?: {
    name: string;
    phone: string;
    email: string;
    address: string;
  };
  inquiryType?: 'general' | 'test_drive' | 'financing' | 'trade_in' | 'purchase';
  onSuccess?: () => void;
}

const defaultDealerInfo = {
  name: 'Motorcentric',
  phone: '+254723-398-457',
  email: '<EMAIL>',
  address: 'Fourways Kiambu road, Nairobi'
};

const ContactForm = ({
  carId,
  carName,
  dealerInfo = defaultDealerInfo,
  inquiryType = 'general',
  onSuccess
}: ContactFormProps) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
    inquiryType: inquiryType,
    preferredContact: 'email',
    bestTimeToCall: '',
    isFinancingNeeded: false,
    hasTradeIn: false,
    agreedToTerms: false
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const inquiryTypes = [
    { value: 'general', label: 'General Inquiry', icon: MessageSquare },
    { value: 'test_drive', label: 'Schedule Test Drive', icon: Car },
    { value: 'financing', label: 'Financing Information', icon: CheckCircle },
    { value: 'trade_in', label: 'Trade-In Valuation', icon: Car },
    { value: 'purchase', label: 'Purchase Inquiry', icon: CheckCircle }
  ];

  const timeSlots = [
    'Morning (9AM - 12PM)',
    'Afternoon (12PM - 5PM)',
    'Evening (5PM - 8PM)',
    'Anytime'
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.agreedToTerms) {
      toast.error('Please agree to the terms and conditions');
      return;
    }

    setIsSubmitting(true);

    try {
      // Save inquiry to Supabase
      const inquiryData = {
        car_id: carId,
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        message: formData.message,
        inquiry_type: formData.inquiryType,
        preferred_contact: formData.preferredContact,
        best_time_to_call: formData.bestTimeToCall,
        is_financing_needed: formData.isFinancingNeeded,
        has_trade_in: formData.hasTradeIn,
        status: 'new',
        created_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('inquiries')
        .insert([inquiryData]);

      if (error) {
        throw error;
      }

      setIsSubmitted(true);
      toast.success('Your inquiry has been sent successfully!');
      
      if (onSuccess) {
        onSuccess();
      }

      // Reset form after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false);
        setFormData({
          name: '',
          email: '',
          phone: '',
          message: '',
          inquiryType: 'general',
          preferredContact: 'email',
          bestTimeToCall: '',
          isFinancingNeeded: false,
          hasTradeIn: false,
          agreedToTerms: false
        });
      }, 3000);

    } catch (error) {
      console.error('Error submitting inquiry:', error);
      toast.error('Failed to send inquiry. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <Card className="max-w-md mx-auto">
        <CardContent className="p-8 text-center">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-charcoal mb-2">
            Thank You!
          </h3>
          <p className="text-muted-foreground mb-4">
            Your inquiry has been sent successfully. We'll get back to you within 24 hours.
          </p>
          {dealerInfo && (
            <div className="text-sm text-muted-foreground">
              <p>You can also reach us directly:</p>
              <p className="font-medium">{dealerInfo.phone}</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="h-5 w-5 mr-2 text-gold" />
          Contact {dealerInfo.name}
        </CardTitle>
        {carName && (
          <Badge variant="secondary" className="w-fit">
            Inquiry about: {carName}
          </Badge>
        )}
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Personal Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Full Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
                placeholder="John Doe"
              />
            </div>
            <div>
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="(*************"
              />
            </div>
            <div>
              <Label htmlFor="inquiry-type">Inquiry Type</Label>
              <Select 
                value={formData.inquiryType} 
                onValueChange={(value) => handleInputChange('inquiryType', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {inquiryTypes.map((type) => {
                    const Icon = type.icon;
                    return (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center">
                          <Icon className="h-4 w-4 mr-2" />
                          {type.label}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Contact Preferences */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="preferred-contact">Preferred Contact Method</Label>
              <Select 
                value={formData.preferredContact} 
                onValueChange={(value) => handleInputChange('preferredContact', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="email">
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-2" />
                      Email
                    </div>
                  </SelectItem>
                  <SelectItem value="phone">
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-2" />
                      Phone Call
                    </div>
                  </SelectItem>
                  <SelectItem value="text">
                    <div className="flex items-center">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Text Message
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="best-time">Best Time to Call</Label>
              <Select 
                value={formData.bestTimeToCall} 
                onValueChange={(value) => handleInputChange('bestTimeToCall', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select time" />
                </SelectTrigger>
                <SelectContent>
                  {timeSlots.map((slot) => (
                    <SelectItem key={slot} value={slot}>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2" />
                        {slot}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Message */}
          <div>
            <Label htmlFor="message">Message *</Label>
            <Textarea
              id="message"
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              required
              placeholder="Please let us know how we can help you..."
              rows={4}
            />
          </div>

          {/* Additional Options */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="financing"
                checked={formData.isFinancingNeeded}
                onCheckedChange={(checked) => handleInputChange('isFinancingNeeded', checked)}
              />
              <Label htmlFor="financing" className="text-sm">
                I'm interested in financing options
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="trade-in"
                checked={formData.hasTradeIn}
                onCheckedChange={(checked) => handleInputChange('hasTradeIn', checked)}
              />
              <Label htmlFor="trade-in" className="text-sm">
                I have a vehicle to trade in
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="terms"
                checked={formData.agreedToTerms}
                onCheckedChange={(checked) => handleInputChange('agreedToTerms', checked)}
              />
              <Label htmlFor="terms" className="text-sm">
                I agree to the terms and conditions and privacy policy *
              </Label>
            </div>
          </div>

          {/* Dealer Info */}
          <div className="bg-muted/30 p-4 rounded-lg">
            <h4 className="font-semibold mb-2 flex items-center">
              <MapPin className="h-4 w-4 mr-2 text-gold" />
              Dealer Information
            </h4>
            <div className="text-sm space-y-1">
              <p className="font-medium">{dealerInfo.name}</p>
              <p>{dealerInfo.address}</p>
              <p>{dealerInfo.phone}</p>
              <p>{dealerInfo.email}</p>
            </div>
          </div>

          {/* Submit Button */}
          <Button 
            type="submit" 
            variant="gold" 
            size="lg" 
            className="w-full"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Sending...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Send Inquiry
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default ContactForm;
