import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  noIndex?: boolean;
  structuredData?: object;
}

const SEO = ({
  title = "Motorcentric - Premium Cars in Kenya | Locally Used, Import Ready & Fresh Imports",
  description = "Discover premium cars in Kenya at Motorcentric. Browse locally used vehicles, import-ready cars, and fresh imports. Located at Fourways Kiambu Road, Nairobi. Expert car dealership with 5+ years of excellence.",
  keywords = "cars Kenya, premium cars Nairobi, locally used cars, import cars Kenya, fresh imports, car dealership Nairobi, Fourways Kiambu Road, luxury cars Kenya, BMW Kenya, Mercedes Kenya, Audi Kenya, Toyota Kenya",
  image = "/lovable-uploads/aeb1d77e-ccf0-4e72-8d38-6e0d2042c6d5.png",
  url = "https://motorcentric.co.ke/",
  type = "website",
  noIndex = false,
  structuredData
}: SEOProps) => {
  const fullImageUrl = image.startsWith('http') ? image : `https://motorcentric.co.ke${image}`;
  const fullUrl = url.startsWith('http') ? url : `https://motorcentric.co.ke${url}`;

  return (
    <Helmet>
      {/* Primary Meta Tags */}
      <title>{title}</title>
      <meta name="title" content={title} />
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      
      {/* Robots */}
      {noIndex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta name="robots" content="index, follow" />
      )}
      
      {/* Canonical URL */}
      <link rel="canonical" href={fullUrl} />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:site_name" content="Motorcentric" />
      <meta property="og:locale" content="en_KE" />
      
      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={fullUrl} />
      <meta property="twitter:title" content={title} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={fullImageUrl} />
      
      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
};

export default SEO;
