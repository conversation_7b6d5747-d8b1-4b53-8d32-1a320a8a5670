import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search } from 'lucide-react';
import { carMakesAndModels } from '@/data/mockCars';

const SimpleSearchFilter = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const [filters, setFilters] = useState({
    make: searchParams.get('make') || '',
    model: searchParams.get('model') || '',
    minPrice: searchParams.get('minPrice') || '',
    maxPrice: searchParams.get('maxPrice') || '',
    searchTerm: searchParams.get('searchTerm') || ''
  });

  const [availableModels, setAvailableModels] = useState<string[]>([]);

  // Update available models when make changes
  useEffect(() => {
    if (filters.make && carMakesAndModels[filters.make]) {
      setAvailableModels(carMakesAndModels[filters.make]);
    } else {
      setAvailableModels([]);
    }
  }, [filters.make]);

  const updateFilter = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value === 'any' ? '' : value };

    // Reset model if make changes
    if (key === 'make') {
      newFilters.model = '';
    }

    setFilters(newFilters);
  };

  const handleSearch = () => {
    const params = new URLSearchParams();

    // Debug: Log the filters being sent
    console.log('SimpleSearchFilter filters:', filters);

    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== '') {
        console.log(`Setting URL param: ${key} = ${value}`);
        params.set(key, value);
      }
    });

    console.log('Final URL params:', params.toString());
    setSearchParams(params);
    navigate(`/search?${params.toString()}`);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <section className="py-8 bg-gradient-elegant">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="bg-black/80 backdrop-blur-sm rounded-2xl p-6 border border-gold/20">
            <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-5 gap-4 items-end">
              
              {/* Car Make */}
              <div>
                <label className="block text-gold text-sm font-medium mb-2">
                  Car Make
                </label>
                <Select value={filters.make || 'any'} onValueChange={(value) => updateFilter('make', value)}>
                  <SelectTrigger className="bg-white border-gold/30 focus:border-gold">
                    <SelectValue placeholder="Any Make" />
                  </SelectTrigger>
                  <SelectContent className="bg-white">
                    <SelectItem value="any">Any Make</SelectItem>
                    {Object.keys(carMakesAndModels).map((make) => (
                      <SelectItem key={make} value={make}>{make}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Car Model */}
              <div>
                <label className="block text-gold text-sm font-medium mb-2">
                  Car Model
                </label>
                <Select
                  value={filters.model || 'any'}
                  onValueChange={(value) => updateFilter('model', value)}
                  disabled={!filters.make}
                >
                  <SelectTrigger className="bg-white border-gold/30 focus:border-gold">
                    <SelectValue placeholder="Enter model" />
                  </SelectTrigger>
                  <SelectContent className="bg-white">
                    <SelectItem value="any">Any Model</SelectItem>
                    {availableModels.map((model) => (
                      <SelectItem key={model} value={model}>{model}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Price Range */}
              <div>
                <label className="block text-gold text-sm font-medium mb-2">
                  Price Range (KSh)
                </label>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    placeholder="0"
                    value={filters.minPrice}
                    onChange={(e) => updateFilter('minPrice', e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="bg-white border-gold/30 focus:border-gold text-sm"
                  />
                  <Input
                    type="number"
                    placeholder="100,000"
                    value={filters.maxPrice}
                    onChange={(e) => updateFilter('maxPrice', e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="bg-white border-gold/30 focus:border-gold text-sm"
                  />
                </div>
                <div className="flex justify-between text-xs text-gold/70 mt-1">
                  <span>KSh 0</span>
                  <span>KSh 100,000</span>
                </div>
              </div>

              {/* Search Term */}
              <div>
                <label className="block text-gold text-sm font-medium mb-2">
                  Search
                </label>
                <Input
                  type="text"
                  placeholder="Search cars..."
                  value={filters.searchTerm}
                  onChange={(e) => updateFilter('searchTerm', e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="bg-white border-gold/30 focus:border-gold"
                />
              </div>

              {/* Search Button */}
              <div>
                <Button
                  onClick={handleSearch}
                  className="w-full bg-gold hover:bg-gold/90 text-black font-semibold py-3 px-6 rounded-lg transition-all duration-300"
                >
                  <Search className="mr-2 h-4 w-4" />
                  SEARCH CARS
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SimpleSearchFilter;
