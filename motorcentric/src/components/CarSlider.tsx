import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import WhatsAppButton from '@/components/WhatsAppButton';
import { ChevronLeft, ChevronRight, Heart, Eye, Fuel, Calendar } from 'lucide-react';
import { mockCars } from '@/data/mockCars';
import { useCars } from '@/hooks/useCars';

const CarSlider = () => {
  const navigate = useNavigate();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [favorites, setFavorites] = useState<Set<number>>(new Set());

  // Fetch featured cars from database
  const { cars: dbCars, loading, error } = useCars({ }, 1, 6);

  // Use database cars or fallback to mock cars
  const cars = (dbCars && dbCars.length > 0) ? dbCars : mockCars;

  // Auto-slide functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % Math.max(1, cars.length - 2));
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % Math.max(1, cars.length - 2));
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + Math.max(1, cars.length - 2)) % Math.max(1, cars.length - 2));
  };

  const toggleFavorite = (carId: number) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(carId)) {
        newFavorites.delete(carId);
      } else {
        newFavorites.add(carId);
      }
      return newFavorites;
    });
  };

  return (
    <section className="py-8 md:py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8 md:mb-12">
          <h2 className="text-2xl md:text-4xl font-bold text-charcoal mb-4">
            Featured Vehicles
          </h2>
          <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
            Explore our handpicked selection of premium vehicles
          </p>
        </div>

        <div className="relative">
          {/* Navigation Buttons */}
          <Button
            variant="elegant"
            size="icon"
            className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 h-12 w-12 rounded-full shadow-lg"
            onClick={prevSlide}
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
          
          <Button
            variant="elegant"
            size="icon"
            className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 h-12 w-12 rounded-full shadow-lg"
            onClick={nextSlide}
          >
            <ChevronRight className="h-6 w-6" />
          </Button>

          {/* Car Cards */}
          <div className="overflow-hidden">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentIndex * (window.innerWidth < 768 ? 100 : window.innerWidth < 1024 ? 50 : 33.333)}%)` }}
            >
              {cars.map((car) => (
                <div key={car.id} className="w-full md:w-1/2 lg:w-1/3 flex-shrink-0 px-2 md:px-4">
                  <Card className="group hover:shadow-elegant transition-all duration-300 overflow-hidden">
                    <div className="relative">
                      <img
                        src={car.featured_image_url || car.image_url || (car.images && car.images[0]) || 'https://images.unsplash.com/photo-1555215695-3004980ad54e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80'}
                        alt={`${car.make} ${car.model}`}
                        className="w-full h-48 md:h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-2 md:top-4 right-2 md:right-4 flex gap-1 md:gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="bg-white/90 hover:bg-white h-8 w-8 md:h-10 md:w-10 rounded-full"
                          onClick={() => toggleFavorite(car.id)}
                        >
                          <Heart 
                            className={`h-4 w-4 md:h-5 md:w-5 ${
                              favorites.has(car.id)
                                ? 'fill-red-500 text-red-500' 
                                : 'text-gray-600'
                            }`}
                          />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="bg-white/90 hover:bg-white h-8 w-8 md:h-10 md:w-10 rounded-full"
                        >
                          <Eye className="h-4 w-4 md:h-5 md:w-5 text-gray-600" />
                        </Button>
                      </div>
                      <Badge className="absolute top-2 md:top-4 left-2 md:left-4 bg-gold text-white text-xs md:text-sm">
                        {car.year}
                      </Badge>
                    </div>
                    
                    <CardContent className="p-4 md:p-6">
                      <div className="mb-3 md:mb-4">
                        <h3 className="text-lg md:text-xl font-bold text-charcoal mb-2">
                          {car.make} {car.model}
                        </h3>
                        <div className="flex items-center justify-between text-xs md:text-sm text-muted-foreground mb-2 md:mb-3">
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 md:h-4 md:w-4 mr-1" />
                            <span>{car.year}</span>
                          </div>
                          <div className="flex items-center">
                            <Fuel className="h-3 w-3 md:h-4 md:w-4 mr-1" />
                            <span>{car.fuel_type}</span>
                          </div>
                          <span>{car.mileage.toLocaleString()} miles</span>
                        </div>
                      </div>

                      <div className="mb-3 md:mb-4">
                        <div className="flex flex-wrap gap-1 md:gap-2">
                          {car.features.slice(0, 2).map((feature, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="text-xl md:text-2xl font-bold text-gold">
                            KSh {car.price.toLocaleString()}
                          </div>
                          <Button
                            variant="gold"
                            size="sm"
                            className="text-xs md:text-sm"
                            onClick={() => navigate(`/car/${car.id}`)}
                          >
                            View Details
                          </Button>
                        </div>
                        <WhatsAppButton
                          carName={`${car.make} ${car.model}`}
                          variant="outline"
                          size="sm"
                          className="w-full text-xs md:text-sm"
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-8 space-x-2">
            {Array.from({ length: Math.max(1, cars.length - 2) }).map((_, index) => (
              <button
                key={index}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-gold' : 'bg-gray-300'
                }`}
                onClick={() => setCurrentIndex(index)}
              />
            ))}
          </div>
        </div>

        <div className="text-center mt-12">
          <Button
            variant="premium"
            size="lg"
            onClick={() => navigate('/search')}
          >
            View All Vehicles
          </Button>
        </div>
      </div>
    </section>
  );
};

export default CarSlider;