import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Phone, Mail, Calendar, MapPin } from 'lucide-react';

const CTASection = () => {
  return (
    <section className="py-8 md:py-16 bg-charcoal">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-center">
          {/* Main CTA */}
          <div className="text-center lg:text-left">
            <h2 className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-white mb-4 md:mb-6">
              Ready to Find Your
              <span className="block text-transparent bg-clip-text bg-gradient-gold">
                Perfect Vehicle?
              </span>
            </h2>
            <p className="text-lg md:text-xl text-gray-300 mb-6 md:mb-8 leading-relaxed">
              Don't wait any longer. Our expert team is standing by to help you 
              find the car of your dreams. Contact us today for a personalized 
              consultation and exclusive offers.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center lg:justify-start">
              <Button variant="gold" size="lg" className="text-base md:text-lg px-6 md:px-8 py-4 md:py-6">
                <Phone className="mr-2" />
                Call Now
              </Button>
              <Button variant="premium" size="lg" className="text-base md:text-lg px-6 md:px-8 py-4 md:py-6">
                <Calendar className="mr-2" />
                Schedule Visit
              </Button>
            </div>
          </div>

          {/* Contact Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
            <Card className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300">
              <CardContent className="p-4 md:p-6 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 md:w-14 md:h-14 bg-gold rounded-full mb-3 md:mb-4">
                  <Phone className="h-6 w-6 md:h-7 md:w-7 text-white" />
                </div>
                <h3 className="text-base md:text-lg font-semibold text-white mb-1 md:mb-2">
                  Call Us
                </h3>
                <p className="text-gold text-base md:text-lg font-bold">
                  +254 700 123 456
                </p>
                <p className="text-gray-300 text-xs md:text-sm mt-1">
                  Mon-Sat 8AM-6PM
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300">
              <CardContent className="p-4 md:p-6 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 md:w-14 md:h-14 bg-gold rounded-full mb-3 md:mb-4">
                  <Mail className="h-6 w-6 md:h-7 md:w-7 text-white" />
                </div>
                <h3 className="text-base md:text-lg font-semibold text-white mb-1 md:mb-2">
                  Email Us
                </h3>
                <p className="text-gold text-base md:text-lg font-bold">
                  <EMAIL>
                </p>
                <p className="text-gray-300 text-xs md:text-sm mt-1">
                  24/7 Response
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300 sm:col-span-2">
              <CardContent className="p-4 md:p-6 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 md:w-14 md:h-14 bg-gold rounded-full mb-3 md:mb-4">
                  <MapPin className="h-6 w-6 md:h-7 md:w-7 text-white" />
                </div>
                <h3 className="text-base md:text-lg font-semibold text-white mb-1 md:mb-2">
                  Visit Our Showroom
                </h3>
                <p className="text-gold text-base md:text-lg font-bold">
                  Fourways Kiambu road, Nairobi
                </p>
                <p className="text-gray-300 text-xs md:text-sm">
                  Premium District, CA 90210
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="mt-12 md:mt-16 text-center">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-xl md:text-2xl font-bold text-white mb-3 md:mb-4">
              Stay Updated with Latest Arrivals
            </h3>
            <p className="text-gray-300 mb-4 md:mb-6 text-sm md:text-base">
              Be the first to know about new luxury vehicles and exclusive offers
            </p>
            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-3 md:px-4 py-2 md:py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold text-sm md:text-base"
              />
              <Button variant="gold" className="px-6 md:px-8 text-sm md:text-base">"
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;