import { Button } from '@/components/ui/button';
import { MessageCircle } from 'lucide-react';

interface WhatsAppButtonProps {
  carName?: string;
  message?: string;
  variant?: 'default' | 'outline' | 'ghost' | 'gold' | 'premium';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const WhatsAppButton = ({ 
  carName, 
  message, 
  variant = 'default',
  size = 'md',
  className = ''
}: WhatsAppButtonProps) => {
  const phoneNumber = '254723398457'; // Motorcentric WhatsApp number
  
  const defaultMessage = carName 
    ? `Hi, I'm interested in the ${carName}. Could you please provide more information?`
    : 'Hi, I would like to inquire about your vehicles.';
  
  const whatsappMessage = message || defaultMessage;
  
  const handleWhatsAppClick = () => {
    const encodedMessage = encodeURIComponent(whatsappMessage);
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <Button 
      variant={variant} 
      size={size} 
      onClick={handleWhatsAppClick}
      className={`${className}`}
    >
      <MessageCircle className="mr-2 h-4 w-4" />
      WhatsApp Inquiry
    </Button>
  );
};

export default WhatsAppButton;
