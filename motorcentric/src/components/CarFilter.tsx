import { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  Search,
  Filter,
  X,
  ChevronDown,
  ChevronUp,
  Bookmark,
  RotateCcw,
  MapPin,
  Calendar,
  Fuel,
  Settings,
  Car,
  Palette
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { carMakesAndModels } from '@/data/mockCars';
import { toast } from 'sonner';

// Enhanced filter interface
interface FilterState {
  searchTerm: string;
  make: string;
  model: string;
  priceRange: [number, number];
  yearRange: [number, number];
  mileageRange: [number, number];
  fuelType: string[];
  transmission: string[];
  bodyType: string[];
  color: string[];
  condition: string[];
  features: string[];
  category: string;
}

// Filter options
const filterOptions = {
  makes: Object.keys(carMakesAndModels),
  models: carMakesAndModels,
  fuelTypes: ['Gasoline', 'Electric', 'Hybrid', 'Diesel'],
  transmissions: ['Automatic', 'Manual', 'CVT'],
  bodyTypes: ['Sedan', 'SUV', 'Coupe', 'Convertible', 'Hatchback', 'Wagon', 'Sportback'],
  colors: ['Black', 'White', 'Silver', 'Gray', 'Blue', 'Red', 'Green', 'Brown', 'Gold', 'Alpine White', 'Obsidian Black', 'Guards Red', 'Nardo Gray', 'Pearl White', 'Arancio Borealis'],
  conditions: ['New', 'Excellent', 'Good', 'Fair'],
  features: [
    'Leather Seats', 'Sunroof', 'Navigation', 'Bluetooth', 'Backup Camera',
    'Heated Seats', 'Premium Audio', 'Keyless Entry', 'Cruise Control',
    'All-Wheel Drive', 'Turbo Engine', 'Sport Package', 'Carbon Fiber',
    'Adaptive Suspension', 'Ceramic Brakes', 'Performance Exhaust'
  ]
};

const CarFilter = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  // Initialize filter state from URL params or defaults
  const [filters, setFilters] = useState<FilterState>({
    searchTerm: searchParams.get('search') || '',
    make: searchParams.get('make') || '',
    model: searchParams.get('model') || '',
    priceRange: [
      parseInt(searchParams.get('minPrice') || '0'),
      parseInt(searchParams.get('maxPrice') || '50000000')
    ],
    yearRange: [
      parseInt(searchParams.get('minYear') || '2015'),
      parseInt(searchParams.get('maxYear') || '2024')
    ],
    mileageRange: [
      parseInt(searchParams.get('minMileage') || '0'),
      parseInt(searchParams.get('maxMileage') || '100000')
    ],
    fuelType: searchParams.get('fuelType')?.split(',').filter(Boolean) || [],
    transmission: searchParams.get('transmission')?.split(',').filter(Boolean) || [],
    bodyType: searchParams.get('bodyType')?.split(',').filter(Boolean) || [],
    color: searchParams.get('color')?.split(',').filter(Boolean) || [],
    condition: searchParams.get('condition')?.split(',').filter(Boolean) || [],
    features: searchParams.get('features')?.split(',').filter(Boolean) || [],
    category: searchParams.get('category') || 'all'
  });

  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [savedSearches, setSavedSearches] = useState<string[]>([]);
  const [availableMakes, setAvailableMakes] = useState<string[]>([]);
  const [availableModels, setAvailableModels] = useState<string[]>([]);

  // Set available makes and models from mock data
  useEffect(() => {
    setAvailableMakes(filterOptions.makes);

    if (filters.make && filterOptions.models[filters.make as keyof typeof filterOptions.models]) {
      setAvailableModels(filterOptions.models[filters.make as keyof typeof filterOptions.models]);
    } else {
      setAvailableModels([]);
    }
  }, [filters.make]);

  // Update URL params when filters change
  const updateUrlParams = useCallback((newFilters: FilterState) => {
    const params = new URLSearchParams();

    if (newFilters.searchTerm) params.set('search', newFilters.searchTerm);
    if (newFilters.make) params.set('make', newFilters.make);
    if (newFilters.model) params.set('model', newFilters.model);
    if (newFilters.priceRange[0] > 0) params.set('minPrice', newFilters.priceRange[0].toString());
    if (newFilters.priceRange[1] < 500000) params.set('maxPrice', newFilters.priceRange[1].toString());
    if (newFilters.yearRange[0] > 2015) params.set('minYear', newFilters.yearRange[0].toString());
    if (newFilters.yearRange[1] < 2024) params.set('maxYear', newFilters.yearRange[1].toString());
    if (newFilters.mileageRange[0] > 0) params.set('minMileage', newFilters.mileageRange[0].toString());
    if (newFilters.mileageRange[1] < 100000) params.set('maxMileage', newFilters.mileageRange[1].toString());
    if (newFilters.fuelType.length > 0) params.set('fuelType', newFilters.fuelType.join(','));
    if (newFilters.transmission.length > 0) params.set('transmission', newFilters.transmission.join(','));
    if (newFilters.bodyType.length > 0) params.set('bodyType', newFilters.bodyType.join(','));
    if (newFilters.color.length > 0) params.set('color', newFilters.color.join(','));
    if (newFilters.condition.length > 0) params.set('condition', newFilters.condition.join(','));
    if (newFilters.features.length > 0) params.set('features', newFilters.features.join(','));
    if (newFilters.category && newFilters.category !== 'all') params.set('category', newFilters.category);

    setSearchParams(params);
  }, [setSearchParams]);

  // Debounced search function
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateUrlParams(filters);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [filters, updateUrlParams]);

  const updateFilter = (key: keyof FilterState, value: any) => {
    setFilters(prev => {
      const newFilters = { ...prev, [key]: value };

      // Reset model when make changes
      if (key === 'make') {
        newFilters.model = '';
      }

      return newFilters;
    });
  };

  const toggleArrayFilter = (key: keyof FilterState, value: string) => {
    setFilters(prev => {
      const currentArray = prev[key] as string[];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];

      return { ...prev, [key]: newArray };
    });
  };

  const clearAllFilters = () => {
    const defaultFilters: FilterState = {
      searchTerm: '',
      make: '',
      model: '',
      priceRange: [0, 50000000],
      yearRange: [2015, 2024],
      mileageRange: [0, 100000],
      fuelType: [],
      transmission: [],
      bodyType: [],
      color: [],
      condition: [],
      features: [],
      category: 'all'
    };
    setFilters(defaultFilters);
    setSearchParams(new URLSearchParams());
  };

  const handleSearch = () => {
    updateFilters(filters);
    navigate('/search');
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.searchTerm) count++;
    if (filters.make) count++;
    if (filters.model) count++;
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 50000000) count++;
    if (filters.yearRange[0] > 2015 || filters.yearRange[1] < 2024) count++;
    if (filters.mileageRange[0] > 0 || filters.mileageRange[1] < 100000) count++;
    if (filters.fuelType.length > 0) count++;
    if (filters.transmission.length > 0) count++;
    if (filters.bodyType.length > 0) count++;
    if (filters.color.length > 0) count++;
    if (filters.condition.length > 0) count++;
    if (filters.features.length > 0) count++;
    if (filters.category && filters.category !== 'all') count++;
    return count;
  };

  return (
    <section className="py-8 md:py-16 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8 md:mb-12">
          <h2 className="text-2xl md:text-4xl font-bold text-charcoal mb-4">
            Find Your Perfect Car
          </h2>
          <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
            Use our advanced filters to discover the car that matches your dreams and budget
          </p>
        </div>

        <Card className="p-4 md:p-8 shadow-elegant max-w-6xl mx-auto">
          {/* Search Bar */}
          <div className="mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
              <Input
                placeholder="Search by make, model, or keyword..."
                value={filters.searchTerm}
                onChange={(e) => updateFilter('searchTerm', e.target.value)}
                className="pl-10 h-12 text-lg"
              />
            </div>
          </div>

          {/* Quick Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
            {/* Car Make */}
            <div>
              <label className="block text-sm font-semibold text-charcoal mb-2">
                <Car className="inline h-4 w-4 mr-1" />
                Car Make
              </label>
              <Select
                value={filters.make}
                onValueChange={(value) => updateFilter('make', value)}
              >
                <SelectTrigger className="h-10 md:h-12">
                  <SelectValue placeholder="Select Brand" />
                </SelectTrigger>
                <SelectContent className="bg-white z-50 max-h-60">
                  {availableMakes.map((make) => (
                    <SelectItem key={make} value={make}>
                      {make}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Car Model */}
            <div>
              <label className="block text-sm font-semibold text-charcoal mb-2">
                <Settings className="inline h-4 w-4 mr-1" />
                Car Model
              </label>
              <Select
                value={filters.model}
                onValueChange={(value) => updateFilter('model', value)}
                disabled={!filters.make}
              >
                <SelectTrigger className="h-10 md:h-12">
                  <SelectValue placeholder="Select Model" />
                </SelectTrigger>
                <SelectContent className="bg-white z-50 max-h-60">
                  {availableModels.map((model) => (
                    <SelectItem key={model} value={model}>
                      {model}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Year Range */}
            <div>
              <label className="block text-sm font-semibold text-charcoal mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Year: {filters.yearRange[0]} - {filters.yearRange[1]}
              </label>
              <div className="px-2 pt-2">
                <Slider
                  value={filters.yearRange}
                  onValueChange={(value) => updateFilter('yearRange', value)}
                  max={2024}
                  min={2015}
                  step={1}
                  className="w-full"
                />
              </div>
            </div>

            {/* Price Range */}
            <div>
              <label className="block text-sm font-semibold text-charcoal mb-2">
                Price: KSh {filters.priceRange[0].toLocaleString()} - KSh {filters.priceRange[1].toLocaleString()}
              </label>
              <div className="px-2 pt-2">
                <Slider
                  value={filters.priceRange}
                  onValueChange={(value) => updateFilter('priceRange', value)}
                  max={50000000}
                  min={0}
                  step={500000}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Category Filter */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-charcoal mb-4">Category</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-semibold text-charcoal mb-2">
                  Vehicle Category
                </label>
                <Select
                  value={filters.category}
                  onValueChange={(value) => updateFilter('category', value)}
                >
                  <SelectTrigger className="h-10 md:h-12">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent className="bg-white z-50">
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="locally_used">Locally Used</SelectItem>
                    <SelectItem value="ready_for_import">Ready for Import</SelectItem>
                    <SelectItem value="fresh_imports">Fresh Imports</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Active Filters Display */}
          {getActiveFilterCount() > 0 && (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-semibold text-charcoal">
                  Active Filters ({getActiveFilterCount()})
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-muted-foreground hover:text-charcoal"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Clear All
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {filters.make && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Make: {filters.make}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => updateFilter('make', '')}
                    />
                  </Badge>
                )}
                {filters.model && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Model: {filters.model}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => updateFilter('model', '')}
                    />
                  </Badge>
                )}
                {filters.category && filters.category !== 'all' && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    Category: {filters.category.replace('_', ' ')}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => updateFilter('category', 'all')}
                    />
                  </Badge>
                )}
                {filters.fuelType.map(fuel => (
                  <Badge key={fuel} variant="secondary" className="flex items-center gap-1">
                    Fuel: {fuel}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => toggleArrayFilter('fuelType', fuel)}
                    />
                  </Badge>
                ))}
                {filters.transmission.map(trans => (
                  <Badge key={trans} variant="secondary" className="flex items-center gap-1">
                    Transmission: {trans}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => toggleArrayFilter('transmission', trans)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Advanced Filters Toggle */}
          <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className="text-gold hover:text-gold-dark mb-4"
              >
                <Filter className="mr-2 h-4 w-4" />
                Advanced Filters
                {isAdvancedOpen ? (
                  <ChevronUp className="ml-2 h-4 w-4" />
                ) : (
                  <ChevronDown className="ml-2 h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>

            <CollapsibleContent className="space-y-6">
              {/* Year and Mileage Ranges */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6 bg-muted/50 rounded-lg">
                <div>
                  <label className="block text-sm font-semibold text-charcoal mb-2">
                    <Calendar className="inline h-4 w-4 mr-1" />
                    Year Range: {filters.yearRange[0]} - {filters.yearRange[1]}
                  </label>
                  <div className="px-2 pt-2">
                    <Slider
                      value={filters.yearRange}
                      onValueChange={(value) => updateFilter('yearRange', value)}
                      max={2024}
                      min={2015}
                      step={1}
                      className="w-full"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-charcoal mb-2">
                    Mileage Range: {filters.mileageRange[0].toLocaleString()} - {filters.mileageRange[1].toLocaleString()} miles
                  </label>
                  <div className="px-2 pt-2">
                    <Slider
                      value={filters.mileageRange}
                      onValueChange={(value) => updateFilter('mileageRange', value)}
                      max={100000}
                      min={0}
                      step={1000}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              {/* Fuel Type */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center">
                    <Fuel className="h-4 w-4 mr-2" />
                    Fuel Type
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {filterOptions.fuelTypes.map((fuel) => (
                      <div key={fuel} className="flex items-center space-x-2">
                        <Checkbox
                          id={`fuel-${fuel}`}
                          checked={filters.fuelType.includes(fuel)}
                          onCheckedChange={() => toggleArrayFilter('fuelType', fuel)}
                        />
                        <label htmlFor={`fuel-${fuel}`} className="text-sm cursor-pointer">
                          {fuel}
                        </label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Transmission */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center">
                    <Settings className="h-4 w-4 mr-2" />
                    Transmission
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {filterOptions.transmissions.map((trans) => (
                      <div key={trans} className="flex items-center space-x-2">
                        <Checkbox
                          id={`trans-${trans}`}
                          checked={filters.transmission.includes(trans)}
                          onCheckedChange={() => toggleArrayFilter('transmission', trans)}
                        />
                        <label htmlFor={`trans-${trans}`} className="text-sm cursor-pointer">
                          {trans}
                        </label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </CollapsibleContent>
          </Collapsible>

          {/* Body Type and Color */}
          <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
            <CollapsibleContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Body Type */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center">
                      <Car className="h-4 w-4 mr-2" />
                      Body Type
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-3">
                      {filterOptions.bodyTypes.map((type) => (
                        <div key={type} className="flex items-center space-x-2">
                          <Checkbox
                            id={`body-${type}`}
                            checked={filters.bodyType.includes(type)}
                            onCheckedChange={() => toggleArrayFilter('bodyType', type)}
                          />
                          <label htmlFor={`body-${type}`} className="text-sm cursor-pointer">
                            {type}
                          </label>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Color */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center">
                      <Palette className="h-4 w-4 mr-2" />
                      Color
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-3">
                      {filterOptions.colors.map((color) => (
                        <div key={color} className="flex items-center space-x-2">
                          <Checkbox
                            id={`color-${color}`}
                            checked={filters.color.includes(color)}
                            onCheckedChange={() => toggleArrayFilter('color', color)}
                          />
                          <label htmlFor={`color-${color}`} className="text-sm cursor-pointer">
                            {color}
                          </label>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Condition */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">Vehicle Condition</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {filterOptions.conditions.map((condition) => (
                      <div key={condition} className="flex items-center space-x-2">
                        <Checkbox
                          id={`condition-${condition}`}
                          checked={filters.condition.includes(condition)}
                          onCheckedChange={() => toggleArrayFilter('condition', condition)}
                        />
                        <label htmlFor={`condition-${condition}`} className="text-sm cursor-pointer">
                          {condition}
                        </label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Features */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    {filterOptions.features.map((feature) => (
                      <div key={feature} className="flex items-center space-x-2">
                        <Checkbox
                          id={`feature-${feature}`}
                          checked={filters.features.includes(feature)}
                          onCheckedChange={() => toggleArrayFilter('features', feature)}
                        />
                        <label htmlFor={`feature-${feature}`} className="text-sm cursor-pointer">
                          {feature}
                        </label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </CollapsibleContent>
          </Collapsible>

          {/* Search Actions */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-8">
            <Button
              variant="gold"
              size="lg"
              onClick={handleSearch}
              className="px-8 md:px-12 py-4 md:py-6 w-full sm:w-auto"
            >
              <Search className="mr-2" />
              Search Cars ({getActiveFilterCount()} filters)
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="w-full sm:w-auto"
              onClick={() => {
                // Save search functionality would go here
                toast.success('Search preferences saved!');
              }}
            >
              <Bookmark className="mr-2 h-4 w-4" />
              Save Search
            </Button>
          </div>
        </Card>
      </div>
    </section>
  );
};

export default CarFilter;