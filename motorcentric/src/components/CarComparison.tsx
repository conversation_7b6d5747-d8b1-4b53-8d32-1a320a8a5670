import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useComparison } from '@/hooks/useComparison';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  X, 
  Scale, 
  Car, 
  Fuel, 
  Calendar, 
  Gauge, 
  Settings, 
  MapPin,
  DollarSign,
  Zap,
  Shield,
  Star,
  Eye,
  Phone
} from 'lucide-react';
import { Car as CarType } from '@/hooks/useCars';
import featuredCar from '@/assets/featured-car.jpg';

const CarComparison = () => {
  const navigate = useNavigate();
  const { comparisonCars, removeFromComparison, clearComparison } = useComparison();
  const [isOpen, setIsOpen] = useState(false);

  if (comparisonCars.length === 0) {
    return null;
  }

  const getComparisonValue = (car: CarType, field: string): string | number => {
    switch (field) {
      case 'price':
        return car.price;
      case 'year':
        return car.year;
      case 'mileage':
        return car.mileage || 0;
      case 'fuel_type':
        return car.fuel_type || 'N/A';
      case 'transmission':
        return car.transmission || 'N/A';
      case 'body_type':
        return car.body_type || 'N/A';
      case 'color':
        return car.color || 'N/A';
      case 'condition':
        return car.condition || 'N/A';
      case 'location':
        return car.location || 'N/A';
      default:
        return 'N/A';
    }
  };

  const getBestValue = (field: string, isHigherBetter: boolean = false) => {
    const values = comparisonCars.map(car => getComparisonValue(car, field));
    if (isHigherBetter) {
      return Math.max(...values.filter(v => typeof v === 'number') as number[]);
    } else {
      return Math.min(...values.filter(v => typeof v === 'number') as number[]);
    }
  };

  const isHighlighted = (car: CarType, field: string, isHigherBetter: boolean = false) => {
    const value = getComparisonValue(car, field);
    const bestValue = getBestValue(field, isHigherBetter);
    return value === bestValue && typeof value === 'number';
  };

  const comparisonFields = [
    { key: 'price', label: 'Price', icon: DollarSign, isHigherBetter: false, format: (v: any) => `KSh ${v.toLocaleString()}` },
    { key: 'year', label: 'Year', icon: Calendar, isHigherBetter: true, format: (v: any) => v.toString() },
    { key: 'mileage', label: 'Mileage', icon: Gauge, isHigherBetter: false, format: (v: any) => `${v.toLocaleString()} miles` },
    { key: 'fuel_type', label: 'Fuel Type', icon: Fuel, isHigherBetter: false, format: (v: any) => v },
    { key: 'transmission', label: 'Transmission', icon: Settings, isHigherBetter: false, format: (v: any) => v },
    { key: 'body_type', label: 'Body Type', icon: Car, isHigherBetter: false, format: (v: any) => v },
    { key: 'color', label: 'Color', icon: Star, isHigherBetter: false, format: (v: any) => v },
    { key: 'condition', label: 'Condition', icon: Shield, isHigherBetter: false, format: (v: any) => v },
    { key: 'location', label: 'Location', icon: MapPin, isHigherBetter: false, format: (v: any) => v },
  ];

  return (
    <>
      {/* Floating Comparison Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button variant="gold" size="lg" className="rounded-full shadow-lg">
              <Scale className="mr-2 h-5 w-5" />
              Compare ({comparisonCars.length})
            </Button>
          </DialogTrigger>
          
          <DialogContent className="max-w-7xl max-h-[90vh] p-0">
            <DialogHeader className="p-6 pb-0">
              <div className="flex items-center justify-between">
                <DialogTitle className="text-2xl font-bold">
                  Car Comparison ({comparisonCars.length} vehicles)
                </DialogTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearComparison}
                >
                  Clear All
                </Button>
              </div>
            </DialogHeader>
            
            <ScrollArea className="max-h-[80vh]">
              <div className="p-6">
                {/* Car Headers */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  {comparisonCars.map((car) => (
                    <Card key={car.id} className="relative">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute top-2 right-2 z-10"
                        onClick={() => removeFromComparison(car.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                      
                      <div className="relative">
                        <img
                          src={car.image_url || featuredCar}
                          alt={`${car.make} ${car.model}`}
                          className="w-full h-48 object-cover rounded-t-lg"
                        />
                        <Badge className="absolute top-2 left-2 bg-gold text-white">
                          {car.year}
                        </Badge>
                      </div>
                      
                      <CardContent className="p-4">
                        <h3 className="text-lg font-bold text-charcoal mb-2">
                          {car.make} {car.model}
                        </h3>
                        <div className="text-2xl font-bold text-gold mb-4">
                          KSh {car.price.toLocaleString()}
                        </div>
                        
                        <div className="flex gap-2 mb-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/car/${car.id}`)}
                          >
                            <Eye className="mr-1 h-4 w-4" />
                            View
                          </Button>
                          <Button variant="premium" size="sm">
                            <Phone className="mr-1 h-4 w-4" />
                            Contact
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <Separator className="my-6" />

                {/* Comparison Table */}
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold">Detailed Comparison</h3>
                  
                  {comparisonFields.map((field) => {
                    const Icon = field.icon;
                    return (
                      <div key={field.key} className="border rounded-lg p-4">
                        <div className="flex items-center mb-3">
                          <Icon className="h-5 w-5 mr-2 text-gold" />
                          <h4 className="font-semibold">{field.label}</h4>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {comparisonCars.map((car) => {
                            const value = getComparisonValue(car, field.key);
                            const highlighted = typeof value === 'number' && 
                              isHighlighted(car, field.key, field.isHigherBetter);
                            
                            return (
                              <div
                                key={car.id}
                                className={`p-3 rounded-lg border ${
                                  highlighted 
                                    ? 'bg-gold/10 border-gold text-gold font-semibold' 
                                    : 'bg-muted/30'
                                }`}
                              >
                                <div className="text-sm text-muted-foreground mb-1">
                                  {car.make} {car.model}
                                </div>
                                <div className="font-medium">
                                  {field.format(value)}
                                </div>
                                {highlighted && (
                                  <Badge variant="secondary" className="mt-1 text-xs">
                                    {field.isHigherBetter ? 'Highest' : 'Lowest'}
                                  </Badge>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    );
                  })}

                  {/* Features Comparison */}
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-3">
                      <Zap className="h-5 w-5 mr-2 text-gold" />
                      <h4 className="font-semibold">Features</h4>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {comparisonCars.map((car) => (
                        <div key={car.id} className="p-3 rounded-lg bg-muted/30">
                          <div className="text-sm text-muted-foreground mb-2">
                            {car.make} {car.model}
                          </div>
                          <div className="space-y-1">
                            {car.features?.slice(0, 5).map((feature, index) => (
                              <div key={index} className="flex items-center text-sm">
                                <div className="w-2 h-2 bg-gold rounded-full mr-2"></div>
                                {feature}
                              </div>
                            ))}
                            {car.features && car.features.length > 5 && (
                              <div className="text-xs text-muted-foreground">
                                +{car.features.length - 5} more features
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};

export default CarComparison;
