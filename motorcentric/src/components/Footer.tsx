import { Link } from 'react-router-dom';
import { Facebook, Twitter, Instagram, Youtube, Phone, Mail, MapPin } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-charcoal text-white">
      <div className="container mx-auto px-4 py-8 md:py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
          {/* Company Info */}
          <div>
            <img 
              src="/lovable-uploads/aeb1d77e-ccf0-4e72-8d38-6e0d2042c6d5.png" 
              alt="Motorcentric" 
              className="h-8 md:h-12 w-auto mb-4 md:mb-6"
            />
            <p className="text-gray-300 mb-4 md:mb-6 leading-relaxed text-sm md:text-base">
              Your premier destination for luxury automobiles in Kenya. Excellence in every detail,
              satisfaction in every experience.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-gold transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-gold transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-gold transition-colors">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-gold transition-colors">
                <Youtube className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-base md:text-lg font-semibold text-gold mb-3 md:mb-4">Quick Links</h3>
            <ul className="space-y-2 md:space-y-3 text-sm md:text-base">
              <li>
                <Link to="/" className="text-gray-300 hover:text-gold transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/search" className="text-gray-300 hover:text-gold transition-colors">
                  Search Cars
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-300 hover:text-gold transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-300 hover:text-gold transition-colors">
                  Contact
                </Link>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-gold transition-colors">
                  Financing
                </a>
              </li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-base md:text-lg font-semibold text-gold mb-3 md:mb-4">Categories</h3>
            <ul className="space-y-2 md:space-y-3 text-sm md:text-base">
              <li>
                <Link to="/locally-used" className="text-gray-300 hover:text-gold transition-colors">
                  Locally Used Cars
                </Link>
              </li>
              <li>
                <Link to="/ready-for-import" className="text-gray-300 hover:text-gold transition-colors">
                  Ready for Import
                </Link>
              </li>
              <li>
                <Link to="/fresh-imports" className="text-gray-300 hover:text-gold transition-colors">
                  Fresh Imports
                </Link>
              </li>
              <li>
                <Link to="/search" className="text-gray-300 hover:text-gold transition-colors">
                  Search All Cars
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-base md:text-lg font-semibold text-gold mb-3 md:mb-4">Contact Info</h3>
            <div className="space-y-3 md:space-y-4 text-sm md:text-base">
              <div className="flex items-start">
                <MapPin className="h-4 w-4 md:h-5 md:w-5 text-gold mr-2 md:mr-3 mt-1 flex-shrink-0" />
                <div>
                  <p className="text-gray-300">Fourways Kiambu road</p>
                  <p className="text-gray-300">Nairobi, Kenya</p>
                </div>
              </div>
              <div className="flex items-center">
                <Phone className="h-4 w-4 md:h-5 md:w-5 text-gold mr-2 md:mr-3 flex-shrink-0" />
                <p className="text-gray-300">+254 700 123 456</p>
              </div>
              <div className="flex items-center">
                <Mail className="h-4 w-4 md:h-5 md:w-5 text-gold mr-2 md:mr-3 flex-shrink-0" />
                <p className="text-gray-300"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 pt-6 md:pt-8 mt-8 md:mt-12">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-gray-400 text-xs md:text-sm text-center md:text-left">
              © 2024 Motorcentric by Wantajo. All rights reserved.
            </p>
            <div className="flex flex-wrap justify-center gap-4 md:gap-6 mt-0">
              <a href="#" className="text-gray-400 hover:text-gold text-xs md:text-sm transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-gold text-xs md:text-sm transition-colors">
                Terms of Service
              </a>
              <a href="#" className="text-gray-400 hover:text-gold text-xs md:text-sm transition-colors">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;