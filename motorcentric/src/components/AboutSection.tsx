import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Award, Shield, Clock, Users } from 'lucide-react';
import { Link } from 'react-router-dom';

const features = [
  {
    icon: Award,
    title: 'Premium Quality',
    description: 'Every vehicle undergoes rigorous inspection to ensure the highest quality standards.'
  },
  {
    icon: Shield,
    title: 'Trusted Service',
    description: 'Transparent pricing and honest dealings have earned us thousands of loyal customers.'
  },
  {
    icon: Clock,
    title: 'Quick Process',
    description: 'Streamlined buying process gets you behind the wheel of your dream car faster.'
  },
  {
    icon: Users,
    title: 'Expert Team',
    description: 'Our automotive specialists provide personalized guidance throughout your journey.'
  }
];

const AboutSection = () => {
  return (
    <section className="py-8 md:py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-center">
          {/* Content */}
          <div>
            <div className="mb-4 md:mb-6">
              <span className="text-gold font-semibold text-base md:text-lg">About Motorcentric</span>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-charcoal mt-2 mb-4 md:mb-6">
                Your Premier Destination for Luxury Automobiles
              </h2>
              <p className="text-base md:text-lg text-muted-foreground leading-relaxed mb-4 md:mb-6">
                With over 5+ years of excellence in the automotive industry, Motorcentric has 
                established itself as the premier destination for luxury vehicle enthusiasts. 
                We pride ourselves on offering an unparalleled selection of the world's finest 
                automobiles, backed by exceptional service and expertise.
              </p>
              <p className="text-base md:text-lg text-muted-foreground leading-relaxed mb-6 md:mb-8">
                Our commitment to quality extends beyond our inventory. Every vehicle in our 
                collection undergoes meticulous inspection and comes with our comprehensive 
                warranty, ensuring your complete peace of mind.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 md:gap-4">
              <Link to="/about">
                <Button variant="gold" size="lg" className="text-sm md:text-base w-full">
                  Learn More About Us
                </Button>
              </Link>
              <Link to="/contact">
                <Button variant="premium" size="lg" className="text-sm md:text-base w-full">
                  Schedule a Visit
                </Button>
              </Link>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
            {features.map((feature, index) => (
              <Card 
                key={index}
                className="group hover:shadow-gold transition-all duration-300 border-2 hover:border-gold/20"
              >
                <CardContent className="p-4 md:p-6 text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 md:w-14 md:h-14 bg-gold/10 rounded-full mb-3 md:mb-4 group-hover:bg-gold group-hover:text-white transition-all duration-300">
                    <feature.icon className="h-6 w-6 md:h-7 md:w-7 text-gold group-hover:text-white" />
                  </div>
                  
                  <h3 className="text-base md:text-lg font-semibold text-charcoal mb-2 md:mb-3">
                    {feature.title}
                  </h3>
                  
                  <p className="text-muted-foreground text-xs md:text-sm leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Stats Bar */}
        <div className="mt-12 md:mt-16 bg-gradient-gold rounded-2xl p-6 md:p-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
            <div className="text-center text-white">
              <div className="text-2xl md:text-3xl font-bold mb-1 md:mb-2">500+</div>
              <div className="text-white/90 text-sm md:text-base">Vehicles Sold</div>
            </div>
            <div className="text-center text-white">
              <div className="text-2xl md:text-3xl font-bold mb-1 md:mb-2">98%</div>
              <div className="text-white/90 text-sm md:text-base">Satisfaction Rate</div>
            </div>
            <div className="text-center text-white">
              <div className="text-2xl md:text-3xl font-bold mb-1 md:mb-2">15+</div>
              <div className="text-white/90 text-sm md:text-base">Years Experience</div>
            </div>
            <div className="text-center text-white">
              <div className="text-2xl md:text-3xl font-bold mb-1 md:mb-2">25</div>
              <div className="text-white/90 text-sm md:text-base">Locations</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;