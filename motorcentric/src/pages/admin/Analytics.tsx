import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart3, 
  TrendingUp, 
  Eye, 
  MessageSquare,
  Car,
  Users,
  DollarSign,
  Calendar
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface AnalyticsData {
  totalCars: number;
  totalInquiries: number;
  totalViews: number;
  averagePrice: number;
  carsByCategory: { [key: string]: number };
  inquiriesByType: { [key: string]: number };
  monthlyInquiries: { [key: string]: number };
  topViewedCars: any[];
}

const Analytics = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    totalCars: 0,
    totalInquiries: 0,
    totalViews: 0,
    averagePrice: 0,
    carsByCategory: {},
    inquiriesByType: {},
    monthlyInquiries: {},
    topViewedCars: []
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30');

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange]);

  const fetchAnalyticsData = async () => {
    try {
      // Fetch cars data
      const { data: cars } = await supabase
        .from('cars')
        .select('*');

      // Fetch inquiries data
      const { data: inquiries } = await supabase
        .from('inquiries')
        .select('*');

      // Fetch analytics events
      const { data: analytics } = await supabase
        .from('analytics')
        .select('*')
        .gte('created_at', new Date(Date.now() - parseInt(timeRange) * 24 * 60 * 60 * 1000).toISOString());

      if (cars) {
        const totalCars = cars.length;
        const averagePrice = cars.reduce((sum, car) => sum + (car.price || 0), 0) / totalCars;
        
        const carsByCategory = cars.reduce((acc, car) => {
          acc[car.category] = (acc[car.category] || 0) + 1;
          return acc;
        }, {} as { [key: string]: number });

        setAnalyticsData(prev => ({
          ...prev,
          totalCars,
          averagePrice,
          carsByCategory
        }));
      }

      if (inquiries) {
        const totalInquiries = inquiries.length;
        
        const inquiriesByType = inquiries.reduce((acc, inquiry) => {
          acc[inquiry.inquiry_type] = (acc[inquiry.inquiry_type] || 0) + 1;
          return acc;
        }, {} as { [key: string]: number });

        const monthlyInquiries = inquiries.reduce((acc, inquiry) => {
          const month = new Date(inquiry.created_at).toLocaleDateString('en-US', { month: 'short' });
          acc[month] = (acc[month] || 0) + 1;
          return acc;
        }, {} as { [key: string]: number });

        setAnalyticsData(prev => ({
          ...prev,
          totalInquiries,
          inquiriesByType,
          monthlyInquiries
        }));
      }

      if (analytics) {
        const totalViews = analytics.filter(event => event.event_type === 'car_view').length;
        setAnalyticsData(prev => ({
          ...prev,
          totalViews
        }));
      }

    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const statsCards = [
    {
      title: 'Total Cars',
      value: analyticsData.totalCars,
      icon: Car,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Total Inquiries',
      value: analyticsData.totalInquiries,
      icon: MessageSquare,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Total Views',
      value: analyticsData.totalViews,
      icon: Eye,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'Average Price',
      value: `KSh ${analyticsData.averagePrice.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-charcoal">Analytics</h1>
          <p className="text-muted-foreground">Track your business performance</p>
        </div>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7">Last 7 days</SelectItem>
            <SelectItem value="30">Last 30 days</SelectItem>
            <SelectItem value="90">Last 90 days</SelectItem>
            <SelectItem value="365">Last year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((stat, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-charcoal">
                    {typeof stat.value === 'number' ? stat.value.toLocaleString() : stat.value}
                  </p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cars by Category */}
        <Card>
          <CardHeader>
            <CardTitle>Cars by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(analyticsData.carsByCategory).map(([category, count]) => (
                <div key={category} className="flex items-center justify-between">
                  <span className="text-sm font-medium capitalize">
                    {category.replace('_', ' ')}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-gold h-2 rounded-full" 
                        style={{ 
                          width: `${(count / analyticsData.totalCars) * 100}%` 
                        }}
                      ></div>
                    </div>
                    <span className="text-sm text-muted-foreground w-8">
                      {count}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Inquiries by Type */}
        <Card>
          <CardHeader>
            <CardTitle>Inquiries by Type</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(analyticsData.inquiriesByType).map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <span className="text-sm font-medium capitalize">
                    {type.replace('_', ' ')}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full" 
                        style={{ 
                          width: `${(count / analyticsData.totalInquiries) * 100}%` 
                        }}
                      ></div>
                    </div>
                    <span className="text-sm text-muted-foreground w-8">
                      {count}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Monthly Inquiry Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(analyticsData.monthlyInquiries).map(([month, count]) => (
              <div key={month} className="flex items-center justify-between">
                <span className="text-sm font-medium">{month}</span>
                <div className="flex items-center space-x-2">
                  <div className="w-48 bg-gray-200 rounded-full h-3">
                    <div 
                      className="bg-green-500 h-3 rounded-full" 
                      style={{ 
                        width: `${(count / Math.max(...Object.values(analyticsData.monthlyInquiries))) * 100}%` 
                      }}
                    ></div>
                  </div>
                  <span className="text-sm text-muted-foreground w-8">
                    {count}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Analytics;
