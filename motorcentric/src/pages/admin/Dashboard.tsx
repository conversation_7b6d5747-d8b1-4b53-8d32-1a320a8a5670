import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Car, 
  Users, 
  MessageSquare, 
  TrendingUp, 
  Eye,
  Plus,
  Filter,
  Search,
  MoreHorizontal
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';

interface DashboardStats {
  totalCars: number;
  availableCars: number;
  totalInquiries: number;
  newInquiries: number;
  recentViews: number;
  featuredCars: number;
}

const Dashboard = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats>({
    totalCars: 0,
    availableCars: 0,
    totalInquiries: 0,
    newInquiries: 0,
    recentViews: 0,
    featuredCars: 0
  });
  const [recentCars, setRecentCars] = useState<any[]>([]);
  const [recentInquiries, setRecentInquiries] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Fetch cars stats
      const { data: cars, error: carsError } = await supabase
        .from('cars')
        .select('*');

      if (carsError) {
        console.error('Error fetching cars:', carsError);
      }

      // Fetch inquiries stats
      const { data: inquiries, error: inquiriesError } = await supabase
        .from('inquiries')
        .select('*');

      if (inquiriesError) {
        console.error('Error fetching inquiries:', inquiriesError);
      }

      // Fetch recent cars
      const { data: recentCarsData, error: recentCarsError } = await supabase
        .from('cars')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);

      if (recentCarsError) {
        console.error('Error fetching recent cars:', recentCarsError);
      }

      // Fetch recent inquiries
      const { data: recentInquiriesData, error: recentInquiriesError } = await supabase
        .from('inquiries')
        .select('*, cars(make, model)')
        .order('created_at', { ascending: false })
        .limit(5);

      if (recentInquiriesError) {
        console.error('Error fetching recent inquiries:', recentInquiriesError);
      }

      if (cars) {
        setStats(prev => ({
          ...prev,
          totalCars: cars.length,
          availableCars: cars.filter(car => car.is_available).length,
          featuredCars: cars.filter(car => car.is_featured).length
        }));
      }

      if (inquiries) {
        setStats(prev => ({
          ...prev,
          totalInquiries: inquiries.length,
          newInquiries: inquiries.filter(inquiry => inquiry.status === 'new').length
        }));
      }

      setRecentCars(recentCarsData || []);
      setRecentInquiries(recentInquiriesData || []);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const statsCards = [
    {
      title: 'Total Cars',
      value: stats.totalCars,
      icon: Car,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Available Cars',
      value: stats.availableCars,
      icon: Eye,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Total Inquiries',
      value: stats.totalInquiries,
      icon: MessageSquare,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'New Inquiries',
      value: stats.newInquiries,
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-charcoal">Dashboard</h1>
          <p className="text-muted-foreground">Welcome to Motorcentric Admin Panel</p>
        </div>
        <Button
          onClick={() => navigate('/admin/cars/new')}
          className="bg-gold hover:bg-gold/90 w-full sm:w-auto"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add New Car
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((stat, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-charcoal">
                    {stat.value}
                  </p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Cars */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Recent Cars</CardTitle>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => navigate('/admin/cars')}
            >
              View All
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentCars.map((car) => (
                <div key={car.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                      <Car className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <div>
                      <p className="font-medium">{car.make} {car.model}</p>
                      <p className="text-sm text-muted-foreground">
                        KSh {car.price?.toLocaleString()} • {car.year}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={car.is_available ? 'default' : 'secondary'}>
                      {car.is_available ? 'Available' : 'Sold'}
                    </Badge>
                    <Badge variant="outline">
                      {car.category?.replace('_', ' ')}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Inquiries */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Recent Inquiries</CardTitle>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => navigate('/admin/inquiries')}
            >
              View All
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentInquiries.map((inquiry) => (
                <div key={inquiry.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{inquiry.customer_name}</p>
                    <p className="text-sm text-muted-foreground">
                      {inquiry.cars?.make} {inquiry.cars?.model} • {inquiry.inquiry_type}
                    </p>
                  </div>
                  <Badge 
                    variant={
                      inquiry.status === 'new' ? 'destructive' :
                      inquiry.status === 'in_progress' ? 'default' :
                      'secondary'
                    }
                  >
                    {inquiry.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
