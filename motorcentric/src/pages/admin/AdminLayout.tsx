import { useState, useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  LayoutDashboard, 
  Car, 
  MessageSquare, 
  Users, 
  BarChart3, 
  Settings,
  LogOut,
  Menu,
  X
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';

const AdminLayout = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, signOut } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userProfile, setUserProfile] = useState<any>(null);

  useEffect(() => {
    checkAdminAccess();
    fetchUserProfile();
  }, [user]);

  const checkAdminAccess = async () => {
    if (!user) {
      navigate('/admin/login');
      return;
    }

    try {
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error checking admin access:', error);
        // If profile doesn't exist, create it for admin user
        if (user.email === '<EMAIL>') {
          await createAdminProfile();
          return;
        }
        navigate('/admin/login');
        return;
      }

      if (!profile || profile.role !== 'admin') {
        navigate('/admin/login');
        return;
      }
    } catch (error) {
      console.error('Admin access check failed:', error);
      navigate('/admin/login');
    }
  };

  const createAdminProfile = async () => {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .insert([
          {
            id: user?.id,
            email: user?.email,
            full_name: 'Motorcentric Admin',
            role: 'admin',
            is_active: true
          }
        ]);

      if (error) {
        console.error('Error creating admin profile:', error);
        navigate('/admin/login');
      }
    } catch (error) {
      console.error('Failed to create admin profile:', error);
      navigate('/admin/login');
    }
  };

  const fetchUserProfile = async () => {
    if (!user) return;

    try {
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        return;
      }

      setUserProfile(profile);
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
    }
  };

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  const navigation = [
    {
      name: 'Dashboard',
      href: '/admin',
      icon: LayoutDashboard,
      current: location.pathname === '/admin'
    },
    {
      name: 'Cars',
      href: '/admin/cars',
      icon: Car,
      current: location.pathname.startsWith('/admin/cars')
    },
    {
      name: 'Inquiries',
      href: '/admin/inquiries',
      icon: MessageSquare,
      current: location.pathname.startsWith('/admin/inquiries')
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: BarChart3,
      current: location.pathname.startsWith('/admin/analytics')
    },
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: Settings,
      current: location.pathname.startsWith('/admin/settings')
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 lg:hidden backdrop-blur-sm"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity"></div>
        </div>
      )}

      {/* Sidebar */}
      <div className={`
        w-64 bg-white shadow-lg flex flex-col h-screen transition-all duration-300 ease-in-out
        ${sidebarOpen ? 'fixed inset-y-0 left-0 z-50 lg:relative lg:translate-x-0' : 'hidden lg:flex'}
      `}>
        <div className="flex items-center justify-between h-16 px-6 border-b flex-shrink-0">
          <div className="flex items-center">
            <img
              src="/lovable-uploads/aeb1d77e-ccf0-4e72-8d38-6e0d2042c6d5.png"
              alt="Motorcentric"
              className="h-8 w-auto"
            />
            <span className="ml-2 text-lg font-semibold text-charcoal">Admin</span>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        <nav className="flex-1 mt-6 px-3 overflow-y-auto">
          <div className="space-y-1">
            {navigation.map((item) => (
              <Button
                key={item.name}
                variant={item.current ? "default" : "ghost"}
                className={`w-full justify-start ${
                  item.current
                    ? 'bg-gold text-white hover:bg-gold/90'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => {
                  navigate(item.href);
                  setSidebarOpen(false);
                }}
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </Button>
            ))}
          </div>
        </nav>

        {/* User Profile */}
        <div className="flex-shrink-0 p-4 border-t">
          <div className="flex items-center space-x-3">
            <Avatar>
              <AvatarImage src={userProfile?.avatar_url} />
              <AvatarFallback>
                {userProfile?.full_name?.charAt(0) || user?.email?.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {userProfile?.full_name || 'Admin User'}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {user?.email}
              </p>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleSignOut}
              className="text-gray-400 hover:text-gray-600"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Top bar */}
        <div className="sticky top-0 z-10 bg-white shadow-sm border-b">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6">
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu className="h-5 w-5" />
            </Button>

            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                Welcome back, {userProfile?.full_name || 'Admin'}
              </span>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 p-6 overflow-auto">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
