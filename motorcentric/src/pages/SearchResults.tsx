import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SimpleSearchFilter from '@/components/SimpleSearchFilter';
import SEO from '@/components/SEO';
import SavedSearches from '@/components/SavedSearches';
import ContactForm from '@/components/ContactForm';
import WhatsAppButton from '@/components/WhatsAppButton';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Heart, Eye, Fuel, Calendar, MapPin, Grid, List, Loader2, Scale } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCars, useCarFiltersFromUrl } from '@/hooks/useCars';
import { useComparison } from '@/hooks/useComparison';
import { toast } from 'sonner';
import { mockCars } from '@/data/mockCars';
import featuredCar from '@/assets/featured-car.jpg';

const SearchResults = () => {
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Get filters from URL
  const filters = useCarFiltersFromUrl();

  // Fetch cars with current filters
  const { cars, loading, error, totalCount, refetch } = useCars(filters, currentPage, 12);

  // Comparison functionality
  const { addToComparison, removeFromComparison, isInComparison } = useComparison();

  const toggleFavorite = (carId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(carId)) {
        newFavorites.delete(carId);
        toast.success('Removed from favorites');
      } else {
        newFavorites.add(carId);
        toast.success('Added to favorites');
      }
      return newFavorites;
    });
  };

  const toggleComparison = (car: any) => {
    if (isInComparison(car.id)) {
      removeFromComparison(car.id);
    } else {
      addToComparison(car);
    }
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
    // In a real implementation, you would update the useCars hook to handle sorting
    refetch();
  };

  const totalPages = Math.ceil(totalCount / 12);

  const renderCarCard = (car: any) => (
    <Card
      key={car.id}
      className={`group hover:shadow-elegant transition-all duration-300 overflow-hidden ${
        viewMode === 'list' ? 'md:flex' : ''
      }`}
    >
      <div className={`relative ${viewMode === 'list' ? 'md:w-1/3' : ''}`}>
        <img
          src={car.featured_image_url || car.image_url || (car.images && car.images[0]) || featuredCar}
          alt={`${car.make} ${car.model}`}
          className={`object-cover group-hover:scale-105 transition-transform duration-300 ${
            viewMode === 'list' ? 'w-full h-48 md:h-full' : 'w-full h-48 sm:h-56 md:h-64'
          }`}
        />
        <div className="absolute top-2 right-2 flex gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="bg-white/90 hover:bg-white h-8 w-8 sm:h-10 sm:w-10 rounded-full"
            onClick={() => toggleFavorite(car.id)}
          >
            <Heart
              className={`h-4 w-4 sm:h-5 sm:w-5 ${
                favorites.has(car.id)
                  ? 'fill-red-500 text-red-500'
                  : 'text-gray-600'
              }`}
            />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className={`bg-white/90 hover:bg-white h-8 w-8 sm:h-10 sm:w-10 rounded-full ${
              isInComparison(car.id) ? 'bg-gold/20 border-gold' : ''
            }`}
            onClick={() => toggleComparison(car)}
          >
            <Scale
              className={`h-4 w-4 sm:h-5 sm:w-5 ${
                isInComparison(car.id) ? 'text-gold' : 'text-gray-600'
              }`}
            />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="bg-white/90 hover:bg-white h-8 w-8 sm:h-10 sm:w-10 rounded-full"
          >
            <Eye className="h-4 w-4 sm:h-5 sm:w-5 text-gray-600" />
          </Button>
        </div>
        <Badge className="absolute top-2 left-2 bg-gold text-white text-xs">
          {car.year}
        </Badge>
      </div>

      <CardContent className={`p-4 sm:p-6 ${viewMode === 'list' ? 'md:w-2/3' : ''}`}>
        <div className="mb-3 sm:mb-4">
          <h3
            className="text-lg sm:text-xl font-bold text-charcoal mb-2 line-clamp-1 cursor-pointer hover:text-gold transition-colors"
            onClick={() => navigate(`/car/${car.id}`)}
          >
            {car.make} {car.model}
          </h3>
          <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-muted-foreground mb-2 sm:mb-3">
            <div className="flex items-center">
              <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
              <span>{car.year}</span>
            </div>
            <div className="flex items-center">
              <Fuel className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
              <span className="truncate">{car.fuel_type}</span>
            </div>
            <span className="truncate">{car.mileage?.toLocaleString()} miles</span>
          </div>
          <div className="flex items-center text-xs sm:text-sm text-muted-foreground mb-2 sm:mb-3">
            <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-1 text-gold flex-shrink-0" />
            <span className="truncate">{car.location}</span>
          </div>
        </div>

        <div className="mb-3 sm:mb-4">
          <div className="flex flex-wrap gap-1 sm:gap-2">
            {car.features?.slice(0, viewMode === 'list' ? 3 : 2).map((feature: string, index: number) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {feature}
              </Badge>
            ))}
            {car.features && car.features.length > (viewMode === 'list' ? 3 : 2) && (
              <Badge variant="secondary" className="text-xs">
                +{car.features.length - (viewMode === 'list' ? 3 : 2)}
              </Badge>
            )}
          </div>
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div className="text-xl sm:text-2xl font-bold text-gold">
            KSh {car.price.toLocaleString()}
          </div>
          <div className="flex flex-col gap-2">
            <div className="flex gap-2">
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="premium" size="sm" className="flex-1 text-xs sm:text-sm">
                    Contact
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <ContactForm
                    carId={car.id}
                    carName={`${car.make} ${car.model}`}
                    dealerInfo={{
                      name: 'Motorcentric',
                      phone: '+254723-398-457',
                      email: '<EMAIL>',
                      address: 'Fourways Kiambu road, Nairobi'
                    }}
                    inquiryType="general"
                  />
                </DialogContent>
              </Dialog>
              <Button
                variant="gold"
                size="sm"
                className="flex-1 text-xs sm:text-sm"
                onClick={() => navigate(`/car/${car.id}`)}
              >
                View Details
              </Button>
            </div>
            <WhatsAppButton
              carName={`${car.make} ${car.model}`}
              variant="outline"
              size="sm"
              className="w-full text-xs sm:text-sm"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-background">
      <SEO
        title="Search Premium Cars in Kenya - Motorcentric"
        description="Search and find your perfect car in Kenya. Browse our extensive collection of locally used cars, import-ready vehicles, and fresh imports at Motorcentric."
        keywords="search cars Kenya, find cars Nairobi, car search engine Kenya, premium cars search, luxury cars Kenya"
        url="/search"
      />
      <Header />

      {/* Filter Section */}
      <SimpleSearchFilter />

      {/* Results Section */}
      <section className="py-8 md:py-16 bg-white">
        <div className="container mx-auto px-4">
          {/* Saved Searches */}
          <SavedSearches />
          {/* Results Header */}
          <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:justify-between md:items-center mb-6 md:mb-8">
            <div className="text-center md:text-left">
              <h2 className="text-xl md:text-2xl font-bold text-charcoal mb-2">
                {loading ? (
                  <Skeleton className="h-6 md:h-8 w-48 mx-auto md:mx-0" />
                ) : (
                  `${totalCount} Vehicle${totalCount !== 1 ? 's' : ''} Found`
                )}
              </h2>
              <div className="text-sm md:text-base text-muted-foreground">
                {loading ? (
                  <Skeleton className="h-4 w-64 mx-auto md:mx-0" />
                ) : (
                  `Showing ${cars.length} of ${totalCount} luxury vehicles`
                )}
              </div>
            </div>

            {/* Mobile Controls */}
            <div className="flex flex-col sm:flex-row items-center gap-3 md:gap-4">
              {/* Sort By */}
              <div className="flex items-center space-x-2 w-full sm:w-auto">
                <span className="text-sm font-medium text-charcoal whitespace-nowrap">Sort:</span>
                <Select value={sortBy} onValueChange={handleSortChange}>
                  <SelectTrigger className="w-full sm:w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-white z-50">
                    <SelectItem value="created_at">Newest First</SelectItem>
                    <SelectItem value="price">Price: Low to High</SelectItem>
                    <SelectItem value="price-desc">Price: High to Low</SelectItem>
                    <SelectItem value="year">Year: Newest First</SelectItem>
                    <SelectItem value="mileage">Mileage: Lowest First</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* View Mode */}
              <div className="flex items-center border rounded-lg">
                <Button
                  variant={viewMode === 'grid' ? 'gold' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none px-3"
                >
                  <Grid className="h-4 w-4" />
                  <span className="ml-1 hidden sm:inline">Grid</span>
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'gold' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none px-3"
                >
                  <List className="h-4 w-4" />
                  <span className="ml-1 hidden sm:inline">List</span>
                </Button>
              </div>
            </div>
          </div>

          {/* Error State */}
          {error && (
            <Alert className="mb-8">
              <AlertDescription>
                {error}. Please try again or contact support if the problem persists.
              </AlertDescription>
            </Alert>
          )}

          {/* Loading State */}
          {loading && (
            <div className={viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
              : 'space-y-6'
            }>
              {Array.from({ length: 6 }).map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <Skeleton className="w-full h-64" />
                  <CardContent className="p-6">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2 mb-4" />
                    <div className="flex gap-2 mb-4">
                      <Skeleton className="h-6 w-16" />
                      <Skeleton className="h-6 w-20" />
                    </div>
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-8 w-24" />
                      <div className="flex gap-2">
                        <Skeleton className="h-8 w-20" />
                        <Skeleton className="h-8 w-24" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* No Results */}
          {!loading && !error && cars.length === 0 && (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🚗</div>
              <h3 className="text-2xl font-bold text-charcoal mb-2">No vehicles found</h3>
              <p className="text-muted-foreground mb-6">
                Try adjusting your search criteria or browse all available vehicles.
              </p>
              <Button variant="gold" onClick={() => navigate('/search')}>
                Browse All Vehicles
              </Button>
            </div>
          )}

          {/* Results Grid/List */}
          {!loading && !error && cars.length > 0 && (
            <>
              <div className={viewMode === 'grid'
                ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8'
                : 'space-y-4 sm:space-y-6'
              }>
                {cars.map((car) => renderCarCard(car))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-8 sm:mt-12">
                  <div className="flex items-center space-x-1 sm:space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={currentPage === 1}
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      className="px-2 sm:px-4"
                    >
                      <span className="hidden sm:inline">Previous</span>
                      <span className="sm:hidden">Prev</span>
                    </Button>

                    {/* Page Numbers - Show fewer on mobile */}
                    {Array.from({ length: Math.min(window.innerWidth < 640 ? 3 : 5, totalPages) }, (_, i) => {
                      const maxVisible = window.innerWidth < 640 ? 3 : 5;
                      const pageNum = Math.max(1, Math.min(totalPages - maxVisible + 1, currentPage - Math.floor(maxVisible / 2))) + i;
                      if (pageNum > totalPages) return null;

                      return (
                        <Button
                          key={pageNum}
                          variant={currentPage === pageNum ? 'gold' : 'outline'}
                          size="sm"
                          onClick={() => setCurrentPage(pageNum)}
                          className="w-8 sm:w-10 h-8 sm:h-10 p-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}

                    {totalPages > (window.innerWidth < 640 ? 3 : 5) && currentPage < totalPages - 1 && (
                      <>
                        <span className="px-1 sm:px-2 text-muted-foreground">...</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(totalPages)}
                          className="w-8 sm:w-10 h-8 sm:h-10 p-0"
                        >
                          {totalPages}
                        </Button>
                      </>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      disabled={currentPage === totalPages}
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      className="px-2 sm:px-4"
                    >
                      <span className="hidden sm:inline">Next</span>
                      <span className="sm:hidden">Next</span>
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default SearchResults;