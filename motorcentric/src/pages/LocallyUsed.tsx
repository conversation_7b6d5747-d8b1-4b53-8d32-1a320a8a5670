import { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SimpleSearchFilter from '@/components/SimpleSearchFilter';
import SEO from '@/components/SEO';
import WhatsAppButton from '@/components/WhatsAppButton';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Heart, Eye, Fuel, Calendar, MapPin, Grid, List, Loader2, Scale } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCars, useCarFiltersFromUrl } from '@/hooks/useCars';
import { useComparison } from '@/hooks/useComparison';
import CarComparison from '@/components/CarComparison';

const LocallyUsed = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('newest');

  // Get filters from URL and add category filter
  const urlFilters = useCarFiltersFromUrl();
  const filters = { ...urlFilters, category: 'locally_used' };
  
  const { cars, loading, error, totalCount } = useCars(filters);
  const { comparisonCars, addToComparison, removeFromComparison, isInComparison } = useComparison();

  // Set category in URL params
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    params.set('category', 'locally_used');
    setSearchParams(params, { replace: true });
  }, []);

  const handleSortChange = (value: string) => {
    setSortBy(value);
    const params = new URLSearchParams(searchParams);
    params.set('sortBy', value);
    setSearchParams(params);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const handleViewCar = (carId: string) => {
    navigate(`/car/${carId}`);
  };

  return (
    <div className="min-h-screen bg-background">
      <SEO
        title="Locally Used Cars in Kenya - Quality Pre-owned Vehicles | Motorcentric"
        description="Browse quality locally used cars in Kenya at Motorcentric. Well-maintained pre-owned vehicles with verified history. Located at Fourways Kiambu Road, Nairobi."
        keywords="locally used cars Kenya, pre-owned cars Nairobi, second hand cars Kenya, used cars Fourways, quality used cars Kenya"
        url="/locally-used"
      />
      <Header />
      
      {/* Hero Section */}
      <section className="bg-gradient-elegant py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Locally Used Cars
          </h1>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Discover quality pre-owned vehicles that have been locally used and maintained in Kenya
          </p>
        </div>
      </section>

      {/* Search Filter */}
      <SimpleSearchFilter />

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Results Header */}
        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:justify-between md:items-center mb-6 md:mb-8">
          <div className="text-center md:text-left">
            <h2 className="text-xl md:text-2xl font-bold text-charcoal mb-2">
              {loading ? (
                <Skeleton className="h-6 md:h-8 w-48 mx-auto md:mx-0" />
              ) : (
                `${totalCount} Locally Used Vehicle${totalCount !== 1 ? 's' : ''} Found`
              )}
            </h2>
            <div className="text-sm md:text-base text-muted-foreground">
              {loading ? (
                <Skeleton className="h-4 w-64 mx-auto md:mx-0" />
              ) : (
                `Showing ${cars.length} of ${totalCount} locally used vehicles`
              )}
            </div>
          </div>

          {/* View Controls */}
          <div className="flex flex-col sm:flex-row gap-4 items-center">
            {/* Sort Dropdown */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-charcoal">Sort by:</span>
              <Select value={sortBy} onValueChange={handleSortChange}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="price_low">Price: Low to High</SelectItem>
                  <SelectItem value="price_high">Price: High to Low</SelectItem>
                  <SelectItem value="mileage_low">Mileage: Low to High</SelectItem>
                  <SelectItem value="year_new">Year: Newest First</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* View Mode Toggle */}
            <div className="flex bg-muted rounded-lg p-1">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="px-3"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="px-3"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Alert className="mb-8">
            <AlertDescription>
              {error}. Please try again or contact support if the problem persists.
            </AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {loading && (
          <div className={viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
            : 'space-y-6'
          }>
            {Array.from({ length: 6 }).map((_, index) => (
              <Card key={index} className="overflow-hidden">
                <Skeleton className="w-full h-64" />
                <CardContent className="p-6">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2 mb-4" />
                  <div className="flex gap-2 mb-4">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                  <div className="flex justify-between items-center">
                    <Skeleton className="h-8 w-24" />
                    <div className="flex gap-2">
                      <Skeleton className="h-8 w-20" />
                      <Skeleton className="h-8 w-24" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Results Grid/List */}
        {!loading && cars.length > 0 && (
          <div className={viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
            : 'space-y-6'
          }>
            {cars.map((car) => (
              <Card key={car.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div className="relative">
                  <img
                    src={car.image_url || car.featured_image_url || 'https://images.unsplash.com/photo-1555215695-3004980ad54e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80'}
                    alt={`${car.make} ${car.model}`}
                    className="w-full h-64 object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      Locally Used
                    </Badge>
                  </div>
                  <div className="absolute top-4 right-4 flex gap-2">
                    <Button
                      size="sm"
                      variant="secondary"
                      className="bg-white/90 hover:bg-white"
                      onClick={() => {
                        if (isInComparison(car.id)) {
                          removeFromComparison(car.id);
                        } else {
                          addToComparison(car);
                        }
                      }}
                    >
                      <Scale className={`h-4 w-4 ${isInComparison(car.id) ? 'text-gold' : ''}`} />
                    </Button>
                    <Button
                      size="sm"
                      variant="secondary"
                      className="bg-white/90 hover:bg-white"
                    >
                      <Heart className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <CardContent className="p-6">
                  <div className="mb-4">
                    <h3 className="text-xl font-bold text-charcoal mb-1">
                      {car.make} {car.model}
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      {car.year} • {car.condition}
                    </p>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    <Badge variant="outline" className="text-xs">
                      <Fuel className="h-3 w-3 mr-1" />
                      {car.fuel_type}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      <Calendar className="h-3 w-3 mr-1" />
                      {car.year}
                    </Badge>
                    {car.mileage && (
                      <Badge variant="outline" className="text-xs">
                        <MapPin className="h-3 w-3 mr-1" />
                        {car.mileage.toLocaleString()} km
                      </Badge>
                    )}
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="text-2xl font-bold text-gold">
                      {formatPrice(car.price)}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewCar(car.id)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button
                        size="sm"
                        variant="gold"
                        onClick={() => handleViewCar(car.id)}
                      >
                        Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* No Results */}
        {!loading && cars.length === 0 && (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="mb-6">
                <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                  <MapPin className="h-12 w-12 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-semibold text-charcoal mb-2">
                  No locally used cars found
                </h3>
                <p className="text-muted-foreground">
                  Try adjusting your search filters or browse our other categories.
                </p>
              </div>
              <Button variant="gold" onClick={() => navigate('/search')}>
                Browse All Cars
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Comparison Component */}
      {comparisonCars.length > 0 && <CarComparison />}
      
      <WhatsAppButton />
      <Footer />
    </div>
  );
};

export default LocallyUsed;
