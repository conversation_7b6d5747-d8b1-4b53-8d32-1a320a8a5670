import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

const TestDB = () => {
  const [cars, setCars] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testConnection = async () => {
      try {
        console.log('Testing database connection...');
        
        const { data, error } = await supabase
          .from('cars')
          .select('*')
          .limit(5);

        if (error) {
          console.error('Database error:', error);
          setError(error.message);
        } else {
          console.log('Database data:', data);
          setCars(data || []);
        }
      } catch (err) {
        console.error('Connection error:', err);
        setError('Connection failed');
      } finally {
        setLoading(false);
      }
    };

    testConnection();
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Database Test</h1>
      <p>Found {cars.length} cars</p>
      <div className="space-y-2">
        {cars.map((car) => (
          <div key={car.id} className="p-2 border rounded">
            <strong>{car.make} {car.model}</strong> - {car.year} - KSh {car.price?.toLocaleString()}
          </div>
        ))}
      </div>
    </div>
  );
};

export default TestDB;
