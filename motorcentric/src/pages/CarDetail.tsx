import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SEO from '@/components/SEO';
import CarGallery from '@/components/CarGallery';
import ContactForm from '@/components/ContactForm';
import WhatsAppButton from '@/components/WhatsAppButton';
import { useCar } from '@/hooks/useCars';
import { useComparison } from '@/hooks/useComparison';
import { useAnalytics } from '@/hooks/useAnalytics';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { mockCars } from '@/data/mockCars';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Heart,
  Share2,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Fuel,
  Gauge,
  Settings,
  Shield,
  Star,
  Eye,
  Car,
  Users,
  Palette,
  Wrench,
  Scale
} from 'lucide-react';
import { toast } from 'sonner';
import featuredCar from '@/assets/featured-car.jpg';

// Mock data - in real app, this would come from Supabase
const mockCarData = {
  id: '1',
  name: 'BMW M5 Competition',
  make: 'BMW',
  model: 'M5 Competition',
  year: 2024,
  price: 125000,
  images: [
    featuredCar,
    featuredCar,
    featuredCar,
    featuredCar,
    featuredCar,
    featuredCar
  ],
  description: 'Experience the pinnacle of luxury performance with the BMW M5 Competition. This exceptional sedan combines breathtaking power with sophisticated elegance, featuring a twin-turbo V8 engine that delivers 617 horsepower. Every detail has been meticulously crafted to provide an unparalleled driving experience.',
  specifications: {
    engine: '4.4L Twin-Turbo V8',
    horsepower: '617 hp',
    torque: '553 lb-ft',
    transmission: '8-Speed Automatic',
    drivetrain: 'All-Wheel Drive',
    acceleration: '0-60 mph in 3.1s',
    topSpeed: '190 mph (limited)',
    fuelEconomy: '15/21 mpg',
    seating: '5 passengers',
    bodyType: 'Sedan',
    doors: '4 doors'
  },
  features: [
    'M Performance Exhaust System',
    'Carbon Fiber Roof',
    'Adaptive M Suspension',
    'M Carbon Ceramic Brakes',
    'Harman Kardon Surround Sound',
    'Heated & Ventilated Seats',
    'Panoramic Moonroof',
    'Wireless Charging',
    'Apple CarPlay & Android Auto',
    'Head-Up Display',
    'Lane Departure Warning',
    'Blind Spot Monitoring'
  ],
  condition: 'Excellent',
  mileage: 1200,
  location: 'Beverly Hills, CA',
  fuelType: 'Gasoline',
  color: 'Alpine White',
  interior: 'Black Merino Leather',
  vin: 'WBSJF0C55NCG12345',
  stockNumber: 'BMW2024001',
  dealer: {
    name: 'Premium Motors Beverly Hills',
    phone: '+****************',
    email: '<EMAIL>',
    address: '123 Luxury Lane, Beverly Hills, CA 90210'
  }
};

const CarDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isFavorite, setIsFavorite] = useState(false);
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);

  // Fetch car data from Supabase
  const { car, loading, error } = useCar(id || '');

  // Fallback to mock data if no car found or for development
  const carData = car || mockCarData;

  // Default dealer info for real car data
  const dealerInfo = carData.dealer || {
    name: 'Motorcentric Showroom',
    phone: '+254 700 123 456',
    email: '<EMAIL>',
    address: 'Fourways Kiambu road, Nairobi'
  };

  // Default specifications for real car data
  const specifications = carData.specifications || {
    engine: carData.engine_size || 'N/A',
    horsepower: carData.horsepower ? `${carData.horsepower} hp` : 'N/A',
    torque: carData.torque || 'N/A',
    transmission: carData.transmission || 'N/A',
    acceleration: carData.acceleration || 'N/A',
    topSpeed: carData.top_speed || 'N/A',
    fuelType: carData.fuel_type || 'N/A',
    bodyType: carData.body_type || 'N/A',
    year: carData.year || 'N/A',
    condition: carData.condition || 'N/A'
  };

  // Default images and features for real car data
  const carImages = carData.images || [carData.image_url || carData.featured_image_url || 'https://images.unsplash.com/photo-1555215695-3004980ad54e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80'];
  const carFeatures = carData.features || ['Standard Features', 'Air Conditioning', 'Power Steering', 'Electric Windows'];

  // Comparison functionality
  const { addToComparison, removeFromComparison, isInComparison } = useComparison();

  // Analytics tracking
  const { trackCarView } = useAnalytics();

  // Track car view when component mounts
  useEffect(() => {
    if (id) {
      trackCarView(id);
    }
  }, [id, trackCarView]);



  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    toast.success(isFavorite ? 'Removed from favorites' : 'Added to favorites');
  };

  const toggleComparison = () => {
    if (isInComparison(carData.id)) {
      removeFromComparison(carData.id);
    } else {
      addToComparison(carData);
    }
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: carData.name,
        text: `Check out this ${carData.name} for $${carData.price.toLocaleString()}`,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard!');
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <Skeleton className="w-full h-96 lg:h-[500px] rounded-lg mb-4" />
              <div className="grid grid-cols-6 gap-2">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Skeleton key={i} className="h-16 rounded-md" />
                ))}
              </div>
            </div>
            <div className="space-y-6">
              <div>
                <Skeleton className="h-8 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-4" />
                <Skeleton className="h-10 w-32 mb-6" />
              </div>
              <div className="space-y-3">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Alert>
            <AlertDescription>
              {error}. Please try again or go back to search results.
            </AlertDescription>
          </Alert>
        </div>
        <Footer />
      </div>
    );
  }

  const carTitle = `${carData.year} ${carData.make} ${carData.model}`;
  const carDescription = `${carTitle} for sale in Kenya at Motorcentric. ${carData.condition} condition, ${carData.fuel_type} engine. Located at Fourways Kiambu Road, Nairobi. Contact us for more details.`;
  const carImage = carImages[0] || '/lovable-uploads/aeb1d77e-ccf0-4e72-8d38-6e0d2042c6d5.png';

  const carStructuredData = {
    "@context": "https://schema.org",
    "@type": "Car",
    "name": carTitle,
    "description": carDescription,
    "brand": {
      "@type": "Brand",
      "name": carData.make
    },
    "model": carData.model,
    "vehicleModelDate": carData.year,
    "fuelType": carData.fuel_type,
    "vehicleTransmission": carData.transmission,
    "mileageFromOdometer": carData.mileage,
    "vehicleCondition": carData.condition,
    "offers": {
      "@type": "Offer",
      "price": carData.price,
      "priceCurrency": "KES",
      "seller": {
        "@type": "AutoDealer",
        "name": "Motorcentric"
      }
    },
    "image": carImage
  };

  return (
    <div className="min-h-screen bg-background">
      <SEO
        title={`${carTitle} for Sale in Kenya - Motorcentric`}
        description={carDescription}
        keywords={`${carData.make} ${carData.model}, ${carData.year} ${carData.make}, ${carData.make} for sale Kenya, ${carData.model} Kenya, premium cars Kenya`}
        url={`/car/${carData.id}`}
        image={carImage}
        structuredData={carStructuredData}
      />
      <Header />
      
      {/* Breadcrumb */}
      <div className="bg-muted/30 py-4">
        <div className="container mx-auto px-4">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <button onClick={() => navigate('/')} className="hover:text-gold">
              Home
            </button>
            <span>/</span>
            <button onClick={() => navigate('/search')} className="hover:text-gold">
              Search Results
            </button>
            <span>/</span>
            <span className="text-charcoal font-medium">{carData.name}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Images */}
          <div className="lg:col-span-2">
            <CarGallery
              images={carImages}
              carName={`${carData.make} ${carData.model}`}
              year={carData.year}
              className="mb-8"
            />
          </div>

          {/* Right Column - Car Info */}
          <div className="space-y-6">
            {/* Header */}
            <div>
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-charcoal mb-2">{carData.make} {carData.model}</h1>
                  <div className="flex items-center space-x-4 text-muted-foreground">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      <span>{carData.year}</span>
                    </div>
                    <div className="flex items-center">
                      <Gauge className="h-4 w-4 mr-1" />
                      <span>{carData.mileage.toLocaleString()} miles</span>
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleFavorite}
                    className="h-10 w-10"
                  >
                    <Heart
                      className={`h-5 w-5 ${
                        isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'
                      }`}
                    />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleComparison}
                    className={`h-10 w-10 ${
                      isInComparison(carData.id) ? 'bg-gold/20 border border-gold' : ''
                    }`}
                  >
                    <Scale
                      className={`h-5 w-5 ${
                        isInComparison(carData.id) ? 'text-gold' : 'text-gray-600'
                      }`}
                    />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleShare}
                    className="h-10 w-10"
                  >
                    <Share2 className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              {/* Price */}
              <div className="text-4xl font-bold text-gold mb-4">
                KSh {carData.price.toLocaleString()}
              </div>

              {/* Key Info */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-gold" />
                  <span className="text-sm">{carData.location}</span>
                </div>
                <div className="flex items-center">
                  <Fuel className="h-4 w-4 mr-2 text-gold" />
                  <span className="text-sm">{carData.fuelType}</span>
                </div>
                <div className="flex items-center">
                  <Shield className="h-4 w-4 mr-2 text-gold" />
                  <span className="text-sm">{carData.condition}</span>
                </div>
                <div className="flex items-center">
                  <Star className="h-4 w-4 mr-2 text-gold" />
                  <span className="text-sm">Certified</span>
                </div>
              </div>
            </div>

            {/* Contact Buttons */}
            <div className="space-y-3">
              <Dialog open={isContactModalOpen} onOpenChange={setIsContactModalOpen}>
                <DialogTrigger asChild>
                  <Button variant="gold" size="lg" className="w-full">
                    <Mail className="mr-2 h-5 w-5" />
                    Contact
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <ContactForm
                    carId={carData.id}
                    carName={`${carData.make} ${carData.model}`}
                    dealerInfo={dealerInfo}
                    inquiryType="general"
                    onSuccess={() => setIsContactModalOpen(false)}
                  />
                </DialogContent>
              </Dialog>

              <Button
                variant="premium"
                size="lg"
                className="w-full"
                onClick={() => window.open(`tel:${dealerInfo.phone}`, '_self')}
              >
                <Phone className="mr-2 h-5 w-5" />
                Call {dealerInfo.phone}
              </Button>

              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" size="lg" className="w-full">
                    <Eye className="mr-2 h-5 w-5" />
                    Schedule Test Drive
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <ContactForm
                    carId={carData.id}
                    carName={`${carData.make} ${carData.model}`}
                    dealerInfo={dealerInfo}
                    inquiryType="test_drive"
                  />
                </DialogContent>
              </Dialog>

              <WhatsAppButton
                carName={`${carData.make} ${carData.model}`}
                variant="gold"
                size="lg"
                className="w-full"
              />
            </div>

            {/* Dealer Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Our Location</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="font-semibold">{dealerInfo.name}</div>
                <div className="text-sm text-muted-foreground">{dealerInfo.address}</div>
                <div className="text-sm">{dealerInfo.phone}</div>
                <div className="text-sm">{dealerInfo.email}</div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Detailed Information Tabs */}
        <div className="mt-12">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="specifications">Specifications</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
              <TabsTrigger value="history">Vehicle History</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Vehicle Overview</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Description</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {carData.description}
                    </p>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                      <Car className="h-8 w-8 mx-auto mb-2 text-gold" />
                      <div className="font-semibold">{carData.color}</div>
                      <div className="text-sm text-muted-foreground">Exterior Color</div>
                    </div>
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                      <Palette className="h-8 w-8 mx-auto mb-2 text-gold" />
                      <div className="font-semibold">{carData.interior}</div>
                      <div className="text-sm text-muted-foreground">Interior</div>
                    </div>
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                      <Shield className="h-8 w-8 mx-auto mb-2 text-gold" />
                      <div className="font-semibold">{carData.condition}</div>
                      <div className="text-sm text-muted-foreground">Condition</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="specifications" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Technical Specifications</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {Object.entries(specifications).map(([key, value]) => (
                      <div key={key} className="flex justify-between items-center py-2 border-b border-muted">
                        <span className="font-medium capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </span>
                        <span className="text-muted-foreground">{value}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="features" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Features & Equipment</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {carFeatures.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-gold rounded-full"></div>
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="history" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Vehicle History</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-2">Vehicle Identification</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>VIN:</span>
                          <span className="font-mono">{carData.vin}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Stock Number:</span>
                          <span>{carData.stockNumber}</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">History Report</h4>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Shield className="h-4 w-4 text-green-500" />
                          <span className="text-sm">No accidents reported</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Shield className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Single owner vehicle</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Shield className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Regular maintenance records</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>



      <Footer />
    </div>
  );
};

export default CarDetail;
