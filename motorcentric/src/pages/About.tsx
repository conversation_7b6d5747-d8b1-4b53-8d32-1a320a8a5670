import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SEO from '@/components/SEO';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Award, Users, Clock, Shield, Star, Target } from 'lucide-react';

const About = () => {
  const values = [
    {
      icon: Award,
      title: 'Excellence',
      description: 'We maintain the highest standards in everything we do, from vehicle selection to customer service.'
    },
    {
      icon: Shield,
      title: 'Integrity',
      description: 'Transparent dealings and honest communication form the foundation of our business relationships.'
    },
    {
      icon: Users,
      title: 'Customer First',
      description: 'Every decision we make is centered around providing exceptional value to our customers.'
    },
    {
      icon: Target,
      title: 'Innovation',
      description: 'We continuously evolve our processes and services to stay ahead in the automotive industry.'
    }
  ];

  const team = [
    {
      name: '<PERSON>',
      position: 'CEO & Founder',
      experience: '15+ Years',
      description: 'Automotive industry veteran with a passion for luxury vehicles in Kenya.'
    },
    {
      name: '<PERSON>',
      position: 'Sales Director',
      experience: '10+ Years',
      description: 'Expert in matching customers with their perfect vehicle.'
    },
    {
      name: '<PERSON>',
      position: 'Service Manager',
      experience: '12+ Years',
      description: 'Ensures every vehicle meets our rigorous quality standards.'
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <SEO
        title="About Motorcentric - Premium Car Dealership in Kenya"
        description="Learn about Motorcentric, Kenya's trusted premium car dealership with 5+ years of excellence. Located at Fourways Kiambu Road, Nairobi. Expert car sales and service."
        keywords="about Motorcentric, car dealership Kenya, Fourways Kiambu Road, premium cars Nairobi, car dealer Kenya"
        url="/about"
      />
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 bg-gradient-elegant text-white">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            About Motorcentric
          </h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto leading-relaxed">
            Dedicated to Excellence in Luxury Automotive Sales in Kenya Since 2020
          </p>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl font-bold text-charcoal mb-8">Our Story</h2>
            <div className="text-lg text-muted-foreground leading-relaxed space-y-6">
              <p>
                Founded in 2020 by automotive enthusiast John Mwangi, Motorcentric began
                as a premium car dealership in Nairobi with a simple mission: to provide discerning
                customers with access to the world's finest automobiles while delivering
                unparalleled service in Kenya.
              </p>
              <p>
                Over the years, we've established ourselves as Nairobi's premier destination for
                luxury vehicles, but our core values remain unchanged. We believe
                that purchasing a luxury vehicle should be an extraordinary experience that
                reflects the quality of the automobile itself.
              </p>
              <p>
                Today, Motorcentric is recognized as Kenya's premier luxury
                automotive retailer, trusted by hundreds of customers who return to us
                for their automotive needs time and time again.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-charcoal mb-4">Our Values</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              The principles that guide everything we do
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center hover:shadow-gold transition-all duration-300">
                <CardContent className="p-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gold/10 rounded-full mb-6">
                    <value.icon className="h-8 w-8 text-gold" />
                  </div>
                  <h3 className="text-xl font-semibold text-charcoal mb-4">
                    {value.title}
                  </h3>
                  <p className="text-muted-foreground">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-charcoal mb-4">Leadership Team</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Meet the experts behind Motorcentric's success
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {team.map((member, index) => (
              <Card key={index} className="text-center hover:shadow-elegant transition-all duration-300">
                <CardContent className="p-8">
                  <div className="w-24 h-24 bg-gradient-gold rounded-full mx-auto mb-6 flex items-center justify-center">
                    <Users className="h-12 w-12 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-charcoal mb-2">
                    {member.name}
                  </h3>
                  <p className="text-gold font-medium mb-2">
                    {member.position}
                  </p>
                  <p className="text-sm text-muted-foreground mb-4">
                    {member.experience}
                  </p>
                  <p className="text-muted-foreground">
                    {member.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gradient-gold text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">2,500+</div>
              <div className="text-white/90">Happy Customers</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">50+</div>
              <div className="text-white/90">Premium Cars</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">25</div>
              <div className="text-white/90">Locations</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">4.9★</div>
              <div className="text-white/90">Customer Rating</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-charcoal text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">Ready to Experience Excellence?</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Visit one of our showrooms and discover why thousands of customers trust 
            Motorcentric for their luxury automotive needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="gold" size="lg">
              Find a Location
            </Button>
            <Button variant="premium" size="lg">
              Schedule a Visit
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default About;