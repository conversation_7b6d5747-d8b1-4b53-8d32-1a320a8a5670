import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SEO from '@/components/SEO';
import WhatsAppButton from '@/components/WhatsAppButton';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Phone, Mail, MapPin, Clock, Send } from 'lucide-react';

const Contact = () => {
  const locations = [
    {
      name: 'Motorcentric Showroom',
      address: 'Fourways Kiambu road, Nairobi, Kenya',
      phone: '+254 700 123 456',
      email: '<EMAIL>',
      hours: 'Mon-Sat: 8AM-6PM, Sun: 9AM-5PM'
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <SEO
        title="Contact Motorcentric - Get in Touch | Premium Cars Kenya"
        description="Contact Motorcentric for premium cars in Kenya. Visit our showroom at Fourways Kiambu Road, Nairobi. Call +254 700 123 456 <NAME_EMAIL>"
        keywords="contact Motorcentric, car dealership contact Kenya, Fourways Kiambu Road contact, premium cars Nairobi contact"
        url="/contact"
      />
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 bg-gradient-elegant text-white">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            Contact Us
          </h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto leading-relaxed">
            Get in touch with our expert team. We're here to help you find your perfect vehicle.
          </p>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="text-2xl text-charcoal">Send us a Message</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">First Name</Label>
                    <Input id="firstName" placeholder="John" />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input id="lastName" placeholder="Doe" />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" placeholder="<EMAIL>" />
                </div>
                
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input id="phone" type="tel" placeholder="+****************" />
                </div>
                
                <div>
                  <Label htmlFor="subject">Subject</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a subject" />
                    </SelectTrigger>
                    <SelectContent className="bg-white z-50">
                      <SelectItem value="general">General Inquiry</SelectItem>
                      <SelectItem value="vehicle">Vehicle Information</SelectItem>
                      <SelectItem value="financing">Financing Options</SelectItem>
                      <SelectItem value="service">Service & Support</SelectItem>
                      <SelectItem value="appointment">Schedule Appointment</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="message">Message</Label>
                  <Textarea 
                    id="message" 
                    placeholder="Tell us how we can help you..."
                    rows={5}
                  />
                </div>
                
                <Button variant="gold" size="lg" className="w-full">
                  <Send className="mr-2 h-5 w-5" />
                  Send Message
                </Button>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <div className="space-y-8">
              <div>
                <h2 className="text-3xl font-bold text-charcoal mb-6">Get in Touch</h2>
                <p className="text-lg text-muted-foreground mb-8">
                  We're here to answer your questions and help you find the perfect luxury vehicle. 
                  Contact us through any of the methods below.
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <Card className="hover:shadow-gold transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <div className="inline-flex items-center justify-center w-14 h-14 bg-gold/10 rounded-full mb-4">
                      <Phone className="h-7 w-7 text-gold" />
                    </div>
                    <h3 className="text-lg font-semibold text-charcoal mb-2">Call Us</h3>
                    <p className="text-gold font-bold">+254 700 123 456</p>
                    <p className="text-sm text-muted-foreground">Mon-Sat 8AM-6PM</p>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-gold transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <div className="inline-flex items-center justify-center w-14 h-14 bg-gold/10 rounded-full mb-4">
                      <Mail className="h-7 w-7 text-gold" />
                    </div>
                    <h3 className="text-lg font-semibold text-charcoal mb-2">Email Us</h3>
                    <p className="text-gold font-bold"><EMAIL></p>
                    <p className="text-sm text-muted-foreground">24/7 Response</p>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions */}
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-charcoal">Quick Actions</h3>
                <div className="flex flex-col space-y-3">
                  <Button variant="premium" className="justify-start">
                    Schedule a Test Drive
                  </Button>
                  <Button variant="premium" className="justify-start">
                    Request Vehicle Information
                  </Button>
                  <Button variant="premium" className="justify-start">
                    Get Financing Quote
                  </Button>
                  <Button variant="premium" className="justify-start">
                    Trade-in Evaluation
                  </Button>
                  <WhatsAppButton
                    variant="gold"
                    className="w-full justify-start"
                    message="Hi, I would like to inquire about your services at Motorcentric."
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Locations */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-charcoal mb-4">Our Locations</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Visit any of our premium showrooms across the country
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {locations.map((location, index) => (
              <Card key={index} className="hover:shadow-elegant transition-all duration-300">
                <CardContent className="p-8">
                  <div className="inline-flex items-center justify-center w-14 h-14 bg-gold/10 rounded-full mb-6">
                    <MapPin className="h-7 w-7 text-gold" />
                  </div>
                  
                  <h3 className="text-xl font-semibold text-charcoal mb-4">
                    {location.name}
                  </h3>
                  
                  <div className="space-y-3 text-muted-foreground">
                    <div className="flex items-start">
                      <MapPin className="h-4 w-4 mr-2 mt-1 text-gold" />
                      <p className="text-sm">{location.address}</p>
                    </div>
                    
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-2 text-gold" />
                      <p className="text-sm">{location.phone}</p>
                    </div>

                    {location.email && (
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 mr-2 text-gold" />
                        <p className="text-sm">{location.email}</p>
                      </div>
                    )}

                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-gold" />
                      <p className="text-sm">{location.hours}</p>
                    </div>
                  </div>
                  
                  <Button variant="gold" className="w-full mt-6">
                    Get Directions
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Contact;