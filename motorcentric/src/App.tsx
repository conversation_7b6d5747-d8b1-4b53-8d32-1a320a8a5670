import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { AuthProvider } from "./hooks/useAuth";
import { ComparisonProvider } from "./hooks/useComparison";
import CarComparison from "./components/CarComparison";
import FloatingWhatsApp from "./components/FloatingWhatsApp";
import Index from "./pages/Index";
import About from "./pages/About";
import Contact from "./pages/Contact";
import SearchResults from "./pages/SearchResults";
import CarDetail from "./pages/CarDetail";
import LocallyUsed from "./pages/LocallyUsed";
import ReadyForImport from "./pages/ReadyForImport";
import FreshImports from "./pages/FreshImports";
import AdminLayout from "./pages/admin/AdminLayout";
import Dashboard from "./pages/admin/Dashboard";
import Cars from "./pages/admin/Cars";
import CarForm from "./pages/admin/CarForm";
import Analytics from "./pages/admin/Analytics";
import Inquiries from "./pages/admin/Inquiries";
import TestDB from "./pages/TestDB";
import AdminLogin from "./pages/AdminLogin";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <ComparisonProvider>
        <TooltipProvider>
          <HelmetProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/about" element={<About />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/search" element={<SearchResults />} />
              <Route path="/car/:id" element={<CarDetail />} />
              <Route path="/locally-used" element={<LocallyUsed />} />
              <Route path="/ready-for-import" element={<ReadyForImport />} />
              <Route path="/fresh-imports" element={<FreshImports />} />
              <Route path="/test-db" element={<TestDB />} />
              <Route path="/admin/login" element={<AdminLogin />} />
              {/* Admin Routes */}
              <Route path="/admin" element={<AdminLayout />}>
                <Route index element={<Dashboard />} />
                <Route path="cars" element={<Cars />} />
                <Route path="cars/new" element={<CarForm />} />
                <Route path="cars/:id/edit" element={<CarForm />} />
                <Route path="inquiries" element={<Inquiries />} />
                <Route path="analytics" element={<Analytics />} />
              </Route>
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
            <CarComparison />
            <FloatingWhatsApp />
          </BrowserRouter>
          </HelmetProvider>
        </TooltipProvider>
      </ComparisonProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
