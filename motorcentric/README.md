# Motorcentric - Premium Cars in Kenya

Motorcentric is a premium car dealership website specializing in locally used cars, import-ready vehicles, and fresh imports in Kenya. Located at Fourways Kiambu Road, Nairobi.

## Features

- **Car Categories**: Browse locally used cars, ready for import vehicles, and fresh imports
- **Advanced Search**: Filter cars by make, model, price range, and more
- **Car Comparison**: Compare multiple vehicles side by side
- **Admin Dashboard**: Complete car inventory management system
- **SEO Optimized**: Full SEO implementation with structured data
- **Responsive Design**: Works perfectly on all devices
- **WhatsApp Integration**: Direct contact through WhatsApp

## Development

### Prerequisites

- Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- Supabase account for database

### Getting Started

```sh
# Clone the repository
git clone https://github.com/godwinmwanzi/motorcentric.git

# Navigate to the project directory
cd motorcentric

# Install dependencies
npm install

# Start the development server
npm run dev
```

### Environment Setup

1. Create a Supabase project
2. Set up the database using the migration files in `supabase/migrations/`
3. Configure environment variables for Supabase connection

### Development Tools

- **Vite**: Fast build tool and development server
- **React**: Frontend framework
- **TypeScript**: Type safety
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: Modern UI components
- **Supabase**: Backend as a service

## Project Structure

```
src/
├── components/          # Reusable UI components
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── data/               # Mock data and constants
├── integrations/       # Supabase integration
└── lib/                # Utility functions

public/
├── sitemap.xml         # SEO sitemap
├── robots.txt          # Search engine directives
└── lovable-uploads/    # Static assets
```

## Key Features

### Car Management
- Browse cars by category (Locally Used, Ready for Import, Fresh Imports)
- Advanced search and filtering
- Car comparison functionality
- Detailed car pages with galleries

### Admin Dashboard
- Complete CRUD operations for cars
- Analytics and reporting
- Inquiry management
- User management

### SEO & Performance
- Comprehensive SEO optimization
- Structured data markup
- Optimized images and assets
- Fast loading times

## Deployment

The application can be deployed to any static hosting service like:
- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront

## Contact

For questions or support, contact:
- Email: <EMAIL>
- Phone: +254 700 123 456
- Location: Fourways Kiambu Road, Nairobi, Kenya
