-- Create cars table
CREATE TABLE public.cars (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    make TEXT NOT NULL,
    model TEXT NOT NULL,
    year INTEGER NOT NULL,
    price DECIMAL(15,2) NOT NULL,
    mileage INTEGER,
    fuel_type TEXT,
    transmission TEXT,
    body_type TEXT,
    color TEXT,
    description TEXT,
    image_url TEXT,
    features TEXT[],
    location TEXT,
    condition TEXT DEFAULT 'used',
    is_featured BOOLEAN DEFAULT false,
    status TEXT DEFAULT 'available' CHECK (status IN ('available', 'sold', 'reserved')),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create profiles table for user management
CREATE TABLE public.profiles (
    id UUID NOT NULL REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
    email TEXT,
    full_name TEXT,
    role TEXT DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.cars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Cars policies (public can view, only admins can modify)
CREATE POLICY "Anyone can view cars" 
ON public.cars 
FOR SELECT 
USING (true);

CREATE POLICY "Admins can insert cars" 
ON public.cars 
FOR INSERT 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

CREATE POLICY "Admins can update cars" 
ON public.cars 
FOR UPDATE 
USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

CREATE POLICY "Admins can delete cars" 
ON public.cars 
FOR DELETE 
USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- Profiles policies
CREATE POLICY "Users can view own profile" 
ON public.profiles 
FOR SELECT 
USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" 
ON public.profiles 
FOR UPDATE 
USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" 
ON public.profiles 
FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER 
LANGUAGE plpgsql 
SECURITY DEFINER
AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name)
    VALUES (
        new.id,
        new.email,
        COALESCE(new.raw_user_meta_data->>'full_name', new.email)
    );
    RETURN new;
END;
$$;

-- Trigger for automatic profile creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for timestamp updates
CREATE TRIGGER update_cars_updated_at
    BEFORE UPDATE ON public.cars
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Insert sample cars data
INSERT INTO public.cars (make, model, year, price, mileage, fuel_type, transmission, body_type, color, description, image_url, features, location, is_featured) VALUES
('Toyota', 'Camry', 2020, 2500000, 45000, 'Petrol', 'Automatic', 'Sedan', 'Silver', 'Well maintained Toyota Camry with excellent fuel economy', '/lovable-uploads/aeb1d77e-ccf0-4e72-8d38-6e0d2042c6d5.png', ARRAY['Air Conditioning', 'Power Steering', 'ABS', 'Airbags'], 'Nairobi', true),
('Honda', 'Civic', 2019, 2200000, 52000, 'Petrol', 'Manual', 'Sedan', 'Blue', 'Reliable Honda Civic in excellent condition', '/lovable-uploads/aeb1d77e-ccf0-4e72-8d38-6e0d2042c6d5.png', ARRAY['Air Conditioning', 'Power Steering', 'ABS'], 'Mombasa', true),
('Nissan', 'X-Trail', 2021, 3200000, 28000, 'Petrol', 'CVT', 'SUV', 'White', 'Family-friendly SUV with spacious interior', '/lovable-uploads/aeb1d77e-ccf0-4e72-8d38-6e0d2042c6d5.png', ARRAY['Air Conditioning', 'Power Steering', 'ABS', 'Airbags', '4WD'], 'Nairobi', true),
('Mazda', 'Demio', 2018, 1800000, 38000, 'Petrol', 'Automatic', 'Hatchback', 'Red', 'Compact and efficient city car', '/lovable-uploads/aeb1d77e-ccf0-4e72-8d38-6e0d2042c6d5.png', ARRAY['Air Conditioning', 'Power Steering'], 'Kisumu', false),
('Subaru', 'Forester', 2020, 3800000, 35000, 'Petrol', 'CVT', 'SUV', 'Green', 'Adventure-ready SUV with AWD capability', '/lovable-uploads/aeb1d77e-ccf0-4e72-8d38-6e0d2042c6d5.png', ARRAY['Air Conditioning', 'Power Steering', 'ABS', 'Airbags', 'AWD'], 'Nairobi', true),
('Mitsubishi', 'Lancer', 2017, 1950000, 62000, 'Petrol', 'Manual', 'Sedan', 'Black', 'Sporty sedan with great performance', '/lovable-uploads/aeb1d77e-ccf0-4e72-8d38-6e0d2042c6d5.png', ARRAY['Air Conditioning', 'Power Steering', 'ABS'], 'Nakuru', false);